{"version": 3, "file": "stackblur.min.js", "sources": ["../src/stackblur.js"], "sourcesContent": ["/* eslint-disable no-bitwise -- used for calculations */\n/* eslint-disable unicorn/prefer-query-selector -- aiming at\n  backward-compatibility */\n/**\n* StackBlur - a fast almost Gaussian Blur For Canvas\n*\n* In case you find this class useful - especially in commercial projects -\n* I am not totally unhappy for a small donation to my PayPal account\n* <EMAIL>\n*\n* Or support me on flattr:\n* {@link https://flattr.com/thing/72791/StackBlur-a-fast-almost-Gaussian-Blur-Effect-for-CanvasJavascript}.\n*\n* @module StackBlur\n* <AUTHOR>\n* Contact: <EMAIL>\n* Website: {@link http://www.quasimondo.com/StackBlurForCanvas/StackBlurDemo.html}\n* Twitter: @quasimondo\n*\n* @copyright (c) 2010 <PERSON>\n*\n* Permission is hereby granted, free of charge, to any person\n* obtaining a copy of this software and associated documentation\n* files (the \"Software\"), to deal in the Software without\n* restriction, including without limitation the rights to use,\n* copy, modify, merge, publish, distribute, sublicense, and/or sell\n* copies of the Software, and to permit persons to whom the\n* Software is furnished to do so, subject to the following\n* conditions:\n*\n* The above copyright notice and this permission notice shall be\n* included in all copies or substantial portions of the Software.\n*\n* THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n* EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES\n* OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n* NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT\n* HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,\n* WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n* FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR\n* OTHER DEALINGS IN THE SOFTWARE.\n*/\n\nconst mulTable = [\n  512, 512, 456, 512, 328, 456, 335, 512, 405, 328, 271, 456, 388, 335, 292,\n  512, 454, 405, 364, 328, 298, 271, 496, 456, 420, 388, 360, 335, 312, 292,\n  273, 512, 482, 454, 428, 405, 383, 364, 345, 328, 312, 298, 284, 271, 259,\n  496, 475, 456, 437, 420, 404, 388, 374, 360, 347, 335, 323, 312, 302, 292,\n  282, 273, 265, 512, 497, 482, 468, 454, 441, 428, 417, 405, 394, 383, 373,\n  364, 354, 345, 337, 328, 320, 312, 305, 298, 291, 284, 278, 271, 265, 259,\n  507, 496, 485, 475, 465, 456, 446, 437, 428, 420, 412, 404, 396, 388, 381,\n  374, 367, 360, 354, 347, 341, 335, 329, 323, 318, 312, 307, 302, 297, 292,\n  287, 282, 278, 273, 269, 265, 261, 512, 505, 497, 489, 482, 475, 468, 461,\n  454, 447, 441, 435, 428, 422, 417, 411, 405, 399, 394, 389, 383, 378, 373,\n  368, 364, 359, 354, 350, 345, 341, 337, 332, 328, 324, 320, 316, 312, 309,\n  305, 301, 298, 294, 291, 287, 284, 281, 278, 274, 271, 268, 265, 262, 259,\n  257, 507, 501, 496, 491, 485, 480, 475, 470, 465, 460, 456, 451, 446, 442,\n  437, 433, 428, 424, 420, 416, 412, 408, 404, 400, 396, 392, 388, 385, 381,\n  377, 374, 370, 367, 363, 360, 357, 354, 350, 347, 344, 341, 338, 335, 332,\n  329, 326, 323, 320, 318, 315, 312, 310, 307, 304, 302, 299, 297, 294, 292,\n  289, 287, 285, 282, 280, 278, 275, 273, 271, 269, 267, 265, 263, 261, 259\n];\n\nconst shgTable = [\n  9, 11, 12, 13, 13, 14, 14, 15, 15, 15, 15, 16, 16, 16, 16, 17,\n  17, 17, 17, 17, 17, 17, 18, 18, 18, 18, 18, 18, 18, 18, 18, 19,\n  19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 20, 20, 20,\n  20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 21,\n  21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21,\n  21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 22, 22, 22, 22, 22, 22,\n  22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22,\n  22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 23,\n  23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23,\n  23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23,\n  23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23,\n  23, 23, 23, 23, 23, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24,\n  24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24,\n  24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24,\n  24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24,\n  24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24\n];\n\n/**\n * @param {string|HTMLImageElement} img\n * @param {string|HTMLCanvasElement} canvas\n * @param {Float} radius\n * @param {boolean} blurAlphaChannel\n * @param {boolean} useOffset\n * @param {boolean} skipStyles\n * @returns {undefined}\n */\nfunction processImage (\n  img, canvas, radius, blurAlphaChannel, useOffset, skipStyles\n) {\n  if (typeof img === 'string') {\n    img = document.getElementById(img);\n  }\n\n  if (\n    !img ||\n    (Object.prototype.toString.call(img).slice(8, -1) ===\n      'HTMLImageElement' && !('naturalWidth' in img))\n  ) {\n    return;\n  }\n\n  const dimensionType = useOffset ? 'offset' : 'natural';\n  let w = img[dimensionType + 'Width'];\n  let h = img[dimensionType + 'Height'];\n\n  // add ImageBitmap support,can blur texture source\n  if (Object.prototype.toString.call(img).slice(8, -1) === 'ImageBitmap') {\n    w = img.width;\n    h = img.height;\n  }\n\n  if (typeof canvas === 'string') {\n    canvas = document.getElementById(canvas);\n  }\n  if (!canvas || !('getContext' in canvas)) {\n    return;\n  }\n\n  if (!skipStyles) {\n    canvas.style.width = w + 'px';\n    canvas.style.height = h + 'px';\n  }\n  canvas.width = w;\n  canvas.height = h;\n\n  const context = canvas.getContext('2d');\n  context.clearRect(0, 0, w, h);\n  context.drawImage(img, 0, 0, img.naturalWidth, img.naturalHeight, 0, 0, w, h);\n\n  if (isNaN(radius) || radius < 1) { return; }\n\n  if (blurAlphaChannel) {\n    processCanvasRGBA(canvas, 0, 0, w, h, radius);\n  } else {\n    processCanvasRGB(canvas, 0, 0, w, h, radius);\n  }\n}\n\n/**\n * @param {string|HTMLCanvasElement} canvas\n * @param {Integer} topX\n * @param {Integer} topY\n * @param {Integer} width\n * @param {Integer} height\n * @throws {Error|TypeError}\n * @returns {ImageData} See {@link https://html.spec.whatwg.org/multipage/canvas.html#imagedata}\n */\nfunction getImageDataFromCanvas (canvas, topX, topY, width, height) {\n  if (typeof canvas === 'string') {\n    canvas = document.getElementById(canvas);\n  }\n  if (!canvas || typeof canvas !== 'object' || !('getContext' in canvas)) {\n    throw new TypeError(\n      'Expecting canvas with `getContext` method ' +\n            'in processCanvasRGB(A) calls!'\n    );\n  }\n\n  const context = canvas.getContext('2d');\n\n  try {\n    return context.getImageData(topX, topY, width, height);\n  } catch (e) {\n    throw new Error('unable to access image data: ' + e);\n  }\n}\n\n/**\n * @param {HTMLCanvasElement} canvas\n * @param {Integer} topX\n * @param {Integer} topY\n * @param {Integer} width\n * @param {Integer} height\n * @param {Float} radius\n * @returns {undefined}\n */\nfunction processCanvasRGBA (canvas, topX, topY, width, height, radius) {\n  if (isNaN(radius) || radius < 1) { return; }\n  radius |= 0;\n\n  let imageData = getImageDataFromCanvas(canvas, topX, topY, width, height);\n\n  imageData = processImageDataRGBA(\n    imageData, topX, topY, width, height, radius\n  );\n\n  canvas.getContext('2d').putImageData(imageData, topX, topY);\n}\n\n/**\n * @param {ImageData} imageData\n * @param {Integer} topX\n * @param {Integer} topY\n * @param {Integer} width\n * @param {Integer} height\n * @param {Float} radius\n * @returns {ImageData}\n */\nfunction processImageDataRGBA (imageData, topX, topY, width, height, radius) {\n  const pixels = imageData.data;\n\n  const div = 2 * radius + 1;\n  // const w4 = width << 2;\n  const widthMinus1 = width - 1;\n  const heightMinus1 = height - 1;\n  const radiusPlus1 = radius + 1;\n  const sumFactor = radiusPlus1 * (radiusPlus1 + 1) / 2;\n\n  const stackStart = new BlurStack();\n  let stack = stackStart;\n  let stackEnd;\n  for (let i = 1; i < div; i++) {\n    stack = stack.next = new BlurStack();\n    if (i === radiusPlus1) {\n      stackEnd = stack;\n    }\n  }\n  stack.next = stackStart;\n\n  let stackIn = null,\n    stackOut = null,\n    yw = 0,\n    yi = 0;\n\n  const mulSum = mulTable[radius];\n  const shgSum = shgTable[radius];\n\n  for (let y = 0; y < height; y++) {\n    stack = stackStart;\n\n    const pr = pixels[yi],\n      pg = pixels[yi + 1],\n      pb = pixels[yi + 2],\n      pa = pixels[yi + 3];\n\n    for (let i = 0; i < radiusPlus1; i++) {\n      stack.r = pr;\n      stack.g = pg;\n      stack.b = pb;\n      stack.a = pa;\n      stack = stack.next;\n    }\n\n    let rInSum = 0, gInSum = 0, bInSum = 0, aInSum = 0,\n      rOutSum = radiusPlus1 * pr,\n      gOutSum = radiusPlus1 * pg,\n      bOutSum = radiusPlus1 * pb,\n      aOutSum = radiusPlus1 * pa,\n      rSum = sumFactor * pr,\n      gSum = sumFactor * pg,\n      bSum = sumFactor * pb,\n      aSum = sumFactor * pa;\n\n    for (let i = 1; i < radiusPlus1; i++) {\n      const p = yi + ((widthMinus1 < i ? widthMinus1 : i) << 2);\n\n      const r = pixels[p],\n        g = pixels[p + 1],\n        b = pixels[p + 2],\n        a = pixels[p + 3];\n\n      const rbs = radiusPlus1 - i;\n      rSum += (stack.r = r) * rbs;\n      gSum += (stack.g = g) * rbs;\n      bSum += (stack.b = b) * rbs;\n      aSum += (stack.a = a) * rbs;\n\n      rInSum += r;\n      gInSum += g;\n      bInSum += b;\n      aInSum += a;\n\n      stack = stack.next;\n    }\n\n    stackIn = stackStart;\n    stackOut = stackEnd;\n    for (let x = 0; x < width; x++) {\n      const paInitial = (aSum * mulSum) >>> shgSum;\n      pixels[yi + 3] = paInitial;\n      if (paInitial !== 0) {\n        const a = 255 / paInitial;\n        pixels[yi] = ((rSum * mulSum) >>> shgSum) * a;\n        pixels[yi + 1] = ((gSum * mulSum) >>> shgSum) * a;\n        pixels[yi + 2] = ((bSum * mulSum) >>> shgSum) * a;\n      } else {\n        pixels[yi] = pixels[yi + 1] = pixels[yi + 2] = 0;\n      }\n\n      rSum -= rOutSum;\n      gSum -= gOutSum;\n      bSum -= bOutSum;\n      aSum -= aOutSum;\n\n      rOutSum -= stackIn.r;\n      gOutSum -= stackIn.g;\n      bOutSum -= stackIn.b;\n      aOutSum -= stackIn.a;\n\n      let p = x + radius + 1;\n      p = (yw + (p < widthMinus1\n        ? p\n        : widthMinus1)) << 2;\n\n      rInSum += (stackIn.r = pixels[p]);\n      gInSum += (stackIn.g = pixels[p + 1]);\n      bInSum += (stackIn.b = pixels[p + 2]);\n      aInSum += (stackIn.a = pixels[p + 3]);\n\n      rSum += rInSum;\n      gSum += gInSum;\n      bSum += bInSum;\n      aSum += aInSum;\n\n      stackIn = stackIn.next;\n\n      const {r, g, b, a} = stackOut;\n\n      rOutSum += r;\n      gOutSum += g;\n      bOutSum += b;\n      aOutSum += a;\n\n      rInSum -= r;\n      gInSum -= g;\n      bInSum -= b;\n      aInSum -= a;\n\n      stackOut = stackOut.next;\n\n      yi += 4;\n    }\n    yw += width;\n  }\n\n  for (let x = 0; x < width; x++) {\n    yi = x << 2;\n\n    let pr = pixels[yi],\n      pg = pixels[yi + 1],\n      pb = pixels[yi + 2],\n      pa = pixels[yi + 3],\n      rOutSum = radiusPlus1 * pr,\n      gOutSum = radiusPlus1 * pg,\n      bOutSum = radiusPlus1 * pb,\n      aOutSum = radiusPlus1 * pa,\n      rSum = sumFactor * pr,\n      gSum = sumFactor * pg,\n      bSum = sumFactor * pb,\n      aSum = sumFactor * pa;\n\n    stack = stackStart;\n\n    for (let i = 0; i < radiusPlus1; i++) {\n      stack.r = pr;\n      stack.g = pg;\n      stack.b = pb;\n      stack.a = pa;\n      stack = stack.next;\n    }\n\n    let yp = width;\n\n    let gInSum = 0, bInSum = 0, aInSum = 0, rInSum = 0;\n    for (let i = 1; i <= radius; i++) {\n      yi = (yp + x) << 2;\n\n      const rbs = radiusPlus1 - i;\n      rSum += (stack.r = (pr = pixels[yi])) * rbs;\n      gSum += (stack.g = (pg = pixels[yi + 1])) * rbs;\n      bSum += (stack.b = (pb = pixels[yi + 2])) * rbs;\n      aSum += (stack.a = (pa = pixels[yi + 3])) * rbs;\n\n      rInSum += pr;\n      gInSum += pg;\n      bInSum += pb;\n      aInSum += pa;\n\n      stack = stack.next;\n\n      if (i < heightMinus1) {\n        yp += width;\n      }\n    }\n\n    yi = x;\n    stackIn = stackStart;\n    stackOut = stackEnd;\n    for (let y = 0; y < height; y++) {\n      let p = yi << 2;\n      pixels[p + 3] = pa = (aSum * mulSum) >>> shgSum;\n      if (pa > 0) {\n        pa = 255 / pa;\n        pixels[p] = ((rSum * mulSum) >>> shgSum) * pa;\n        pixels[p + 1] = ((gSum * mulSum) >>> shgSum) * pa;\n        pixels[p + 2] = ((bSum * mulSum) >>> shgSum) * pa;\n      } else {\n        pixels[p] = pixels[p + 1] = pixels[p + 2] = 0;\n      }\n\n      rSum -= rOutSum;\n      gSum -= gOutSum;\n      bSum -= bOutSum;\n      aSum -= aOutSum;\n\n      rOutSum -= stackIn.r;\n      gOutSum -= stackIn.g;\n      bOutSum -= stackIn.b;\n      aOutSum -= stackIn.a;\n\n      p = (x + (\n        ((p = y + radiusPlus1) < heightMinus1 ? p : heightMinus1) *\n                width\n      )) << 2;\n\n      rSum += (rInSum += (stackIn.r = pixels[p]));\n      gSum += (gInSum += (stackIn.g = pixels[p + 1]));\n      bSum += (bInSum += (stackIn.b = pixels[p + 2]));\n      aSum += (aInSum += (stackIn.a = pixels[p + 3]));\n\n      stackIn = stackIn.next;\n\n      rOutSum += (pr = stackOut.r);\n      gOutSum += (pg = stackOut.g);\n      bOutSum += (pb = stackOut.b);\n      aOutSum += (pa = stackOut.a);\n\n      rInSum -= pr;\n      gInSum -= pg;\n      bInSum -= pb;\n      aInSum -= pa;\n\n      stackOut = stackOut.next;\n\n      yi += width;\n    }\n  }\n  return imageData;\n}\n\n/**\n * @param {HTMLCanvasElement} canvas\n * @param {Integer} topX\n * @param {Integer} topY\n * @param {Integer} width\n * @param {Integer} height\n * @param {Float} radius\n * @returns {undefined}\n */\nfunction processCanvasRGB (canvas, topX, topY, width, height, radius) {\n  if (isNaN(radius) || radius < 1) { return; }\n  radius |= 0;\n\n  let imageData = getImageDataFromCanvas(canvas, topX, topY, width, height);\n  imageData = processImageDataRGB(\n    imageData, topX, topY, width, height, radius\n  );\n\n  canvas.getContext('2d').putImageData(imageData, topX, topY);\n}\n\n/**\n * @param {ImageData} imageData\n * @param {Integer} topX\n * @param {Integer} topY\n * @param {Integer} width\n * @param {Integer} height\n * @param {Float} radius\n * @returns {ImageData}\n */\nfunction processImageDataRGB (imageData, topX, topY, width, height, radius) {\n  const pixels = imageData.data;\n\n  const div = 2 * radius + 1;\n  // const w4 = width << 2;\n  const widthMinus1 = width - 1;\n  const heightMinus1 = height - 1;\n  const radiusPlus1 = radius + 1;\n  const sumFactor = radiusPlus1 * (radiusPlus1 + 1) / 2;\n\n  const stackStart = new BlurStack();\n  let stack = stackStart;\n  let stackEnd;\n  for (let i = 1; i < div; i++) {\n    stack = stack.next = new BlurStack();\n    if (i === radiusPlus1) {\n      stackEnd = stack;\n    }\n  }\n  stack.next = stackStart;\n  let stackIn = null;\n  let stackOut = null;\n\n  const mulSum = mulTable[radius];\n  const shgSum = shgTable[radius];\n\n  let p, rbs;\n  let yw = 0, yi = 0;\n\n  for (let y = 0; y < height; y++) {\n    let pr = pixels[yi],\n      pg = pixels[yi + 1],\n      pb = pixels[yi + 2],\n      rOutSum = radiusPlus1 * pr,\n      gOutSum = radiusPlus1 * pg,\n      bOutSum = radiusPlus1 * pb,\n      rSum = sumFactor * pr,\n      gSum = sumFactor * pg,\n      bSum = sumFactor * pb;\n\n    stack = stackStart;\n\n    for (let i = 0; i < radiusPlus1; i++) {\n      stack.r = pr;\n      stack.g = pg;\n      stack.b = pb;\n      stack = stack.next;\n    }\n\n    let rInSum = 0, gInSum = 0, bInSum = 0;\n    for (let i = 1; i < radiusPlus1; i++) {\n      p = yi + ((widthMinus1 < i ? widthMinus1 : i) << 2);\n      rSum += (stack.r = (pr = pixels[p])) * (rbs = radiusPlus1 - i);\n      gSum += (stack.g = (pg = pixels[p + 1])) * rbs;\n      bSum += (stack.b = (pb = pixels[p + 2])) * rbs;\n\n      rInSum += pr;\n      gInSum += pg;\n      bInSum += pb;\n\n      stack = stack.next;\n    }\n\n    stackIn = stackStart;\n    stackOut = stackEnd;\n    for (let x = 0; x < width; x++) {\n      pixels[yi] = (rSum * mulSum) >>> shgSum;\n      pixels[yi + 1] = (gSum * mulSum) >>> shgSum;\n      pixels[yi + 2] = (bSum * mulSum) >>> shgSum;\n\n      rSum -= rOutSum;\n      gSum -= gOutSum;\n      bSum -= bOutSum;\n\n      rOutSum -= stackIn.r;\n      gOutSum -= stackIn.g;\n      bOutSum -= stackIn.b;\n\n      p = (yw + (\n        (p = x + radius + 1) < widthMinus1 ? p : widthMinus1\n      )) << 2;\n\n      rInSum += (stackIn.r = pixels[p]);\n      gInSum += (stackIn.g = pixels[p + 1]);\n      bInSum += (stackIn.b = pixels[p + 2]);\n\n      rSum += rInSum;\n      gSum += gInSum;\n      bSum += bInSum;\n\n      stackIn = stackIn.next;\n\n      rOutSum += (pr = stackOut.r);\n      gOutSum += (pg = stackOut.g);\n      bOutSum += (pb = stackOut.b);\n\n      rInSum -= pr;\n      gInSum -= pg;\n      bInSum -= pb;\n\n      stackOut = stackOut.next;\n\n      yi += 4;\n    }\n    yw += width;\n  }\n\n  for (let x = 0; x < width; x++) {\n    yi = x << 2;\n    let pr = pixels[yi],\n      pg = pixels[yi + 1],\n      pb = pixels[yi + 2],\n      rOutSum = radiusPlus1 * pr,\n      gOutSum = radiusPlus1 * pg,\n      bOutSum = radiusPlus1 * pb,\n      rSum = sumFactor * pr,\n      gSum = sumFactor * pg,\n      bSum = sumFactor * pb;\n\n    stack = stackStart;\n\n    for (let i = 0; i < radiusPlus1; i++) {\n      stack.r = pr;\n      stack.g = pg;\n      stack.b = pb;\n      stack = stack.next;\n    }\n\n    let rInSum = 0, gInSum = 0, bInSum = 0;\n    for (let i = 1, yp = width; i <= radius; i++) {\n      yi = (yp + x) << 2;\n\n      rSum += (stack.r = (pr = pixels[yi])) * (rbs = radiusPlus1 - i);\n      gSum += (stack.g = (pg = pixels[yi + 1])) * rbs;\n      bSum += (stack.b = (pb = pixels[yi + 2])) * rbs;\n\n      rInSum += pr;\n      gInSum += pg;\n      bInSum += pb;\n\n      stack = stack.next;\n\n      if (i < heightMinus1) {\n        yp += width;\n      }\n    }\n\n    yi = x;\n    stackIn = stackStart;\n    stackOut = stackEnd;\n    for (let y = 0; y < height; y++) {\n      p = yi << 2;\n      pixels[p] = (rSum * mulSum) >>> shgSum;\n      pixels[p + 1] = (gSum * mulSum) >>> shgSum;\n      pixels[p + 2] = (bSum * mulSum) >>> shgSum;\n\n      rSum -= rOutSum;\n      gSum -= gOutSum;\n      bSum -= bOutSum;\n\n      rOutSum -= stackIn.r;\n      gOutSum -= stackIn.g;\n      bOutSum -= stackIn.b;\n\n      p = (x + (\n        ((p = y + radiusPlus1) < heightMinus1 ? p : heightMinus1) *\n                width\n      )) << 2;\n\n      rSum += (rInSum += (stackIn.r = pixels[p]));\n      gSum += (gInSum += (stackIn.g = pixels[p + 1]));\n      bSum += (bInSum += (stackIn.b = pixels[p + 2]));\n\n      stackIn = stackIn.next;\n\n      rOutSum += (pr = stackOut.r);\n      gOutSum += (pg = stackOut.g);\n      bOutSum += (pb = stackOut.b);\n\n      rInSum -= pr;\n      gInSum -= pg;\n      bInSum -= pb;\n\n      stackOut = stackOut.next;\n\n      yi += width;\n    }\n  }\n\n  return imageData;\n}\n\n/**\n *\n */\nexport class BlurStack {\n  /**\n   * Set properties.\n   */\n  constructor () {\n    this.r = 0;\n    this.g = 0;\n    this.b = 0;\n    this.a = 0;\n    this.next = null;\n  }\n}\n\nexport {\n  /**\n    * @function module:StackBlur.image\n    * @see module:StackBlur~processImage\n    */\n  processImage as image,\n  /**\n    * @function module:StackBlur.canvasRGBA\n    * @see module:StackBlur~processCanvasRGBA\n    */\n  processCanvasRGBA as canvasRGBA,\n  /**\n    * @function module:StackBlur.canvasRGB\n    * @see module:StackBlur~processCanvasRGB\n    */\n  processCanvasRGB as canvasRGB,\n  /**\n    * @function module:StackBlur.imageDataRGBA\n    * @see module:StackBlur~processImageDataRGBA\n    */\n  processImageDataRGBA as imageDataRGBA,\n  /**\n    * @function module:StackBlur.imageDataRGB\n    * @see module:StackBlur~processImageDataRGB\n    */\n  processImageDataRGB as imageDataRGB\n};\n"], "names": ["mulTable", "shgTable", "getImageDataFromCanvas", "canvas", "topX", "topY", "width", "height", "document", "getElementById", "_typeof", "TypeError", "context", "getContext", "getImageData", "e", "Error", "processCanvasRGBA", "radius", "isNaN", "imageData", "processImageDataRGBA", "putImageData", "stackEnd", "pixels", "data", "div", "widthMinus1", "heightMinus1", "radiusPlus1", "sumFactor", "stackStart", "BlurStack", "stack", "i", "next", "stackIn", "stackOut", "yw", "yi", "mulSum", "shgSum", "y", "pr", "pg", "pb", "pa", "r", "g", "b", "a", "rInSum", "gInSum", "bInSum", "aInSum", "rOutSum", "gOutSum", "bOutSum", "aOutSum", "rSum", "gSum", "bSum", "aSum", "p", "rbs", "x", "paInitial", "yp", "processCanvasRGB", "processImageDataRGB", "img", "blurAlphaChannel", "useOffset", "skip<PERSON><PERSON><PERSON>", "Object", "prototype", "toString", "call", "slice", "dimensionType", "w", "h", "style", "clearRect", "drawImage", "naturalWidth", "naturalHeight"], "mappings": "sdA2CA,IAAMA,EAAW,CACf,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACtE,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACtE,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACtE,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACtE,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACtE,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACtE,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACtE,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACtE,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACtE,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACtE,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACtE,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACtE,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACtE,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACtE,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACtE,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IACtE,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KAGlEC,EAAW,CACf,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAC3D,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAC5D,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAC5D,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAC5D,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAC5D,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAC5D,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAC5D,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAC5D,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAC5D,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAC5D,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAC5D,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAC5D,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAC5D,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAC5D,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAC5D,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAyE1D,SAASC,EAAwBC,EAAQC,EAAMC,EAAMC,EAAOC,MACpC,iBAAXJ,IACTA,EAASK,SAASC,eAAeN,KAE9BA,GAA4B,WAAlBO,EAAOP,MAAyB,eAAgBA,SACvD,IAAIQ,UACR,+EAKEC,EAAUT,EAAOU,WAAW,iBAGzBD,EAAQE,aAAaV,EAAMC,EAAMC,EAAOC,GAC/C,MAAOQ,SACD,IAAIC,MAAM,gCAAkCD,IAatD,SAASE,EAAmBd,EAAQC,EAAMC,EAAMC,EAAOC,EAAQW,QACzDC,MAAMD,IAAWA,EAAS,IAC9BA,GAAU,MAENE,EAAYlB,EAAuBC,EAAQC,EAAMC,EAAMC,EAAOC,GAElEa,EAAYC,EACVD,EAAWhB,EAAMC,EAAMC,EAAOC,EAAQW,GAGxCf,EAAOU,WAAW,MAAMS,aAAaF,EAAWhB,EAAMC,IAYxD,SAASgB,EAAsBD,EAAWhB,EAAMC,EAAMC,EAAOC,EAAQW,WAY/DK,EAXEC,EAASJ,EAAUK,KAEnBC,EAAM,EAAIR,EAAS,EAEnBS,EAAcrB,EAAQ,EACtBsB,EAAerB,EAAS,EACxBsB,EAAcX,EAAS,EACvBY,EAAYD,GAAeA,EAAc,GAAK,EAE9CE,EAAa,IAAIC,EACnBC,EAAQF,EAEHG,EAAI,EAAGA,EAAIR,EAAKQ,IACvBD,EAAQA,EAAME,KAAO,IAAIH,EACrBE,IAAML,IACRN,EAAWU,GAGfA,EAAME,KAAOJ,UAETK,EAAU,KACZC,EAAW,KACXC,EAAK,EACLC,EAAK,EAEDC,EAASxC,EAASkB,GAClBuB,EAASxC,EAASiB,GAEfwB,EAAI,EAAGA,EAAInC,EAAQmC,IAAK,CAC/BT,EAAQF,UAEFY,EAAKnB,EAAOe,GAChBK,EAAKpB,EAAOe,EAAK,GACjBM,EAAKrB,EAAOe,EAAK,GACjBO,EAAKtB,EAAOe,EAAK,GAEVL,EAAI,EAAGA,EAAIL,EAAaK,IAC/BD,EAAMc,EAAIJ,EACVV,EAAMe,EAAIJ,EACVX,EAAMgB,EAAIJ,EACVZ,EAAMiB,EAAIJ,EACVb,EAAQA,EAAME,aAGZgB,EAAS,EAAGC,EAAS,EAAGC,EAAS,EAAGC,EAAS,EAC/CC,EAAU1B,EAAcc,EACxBa,EAAU3B,EAAce,EACxBa,EAAU5B,EAAcgB,EACxBa,EAAU7B,EAAciB,EACxBa,EAAO7B,EAAYa,EACnBiB,EAAO9B,EAAYc,EACnBiB,EAAO/B,EAAYe,EACnBiB,EAAOhC,EAAYgB,EAEZZ,EAAI,EAAGA,EAAIL,EAAaK,IAAK,KAC9B6B,EAAIxB,IAAOZ,EAAcO,EAAIP,EAAcO,IAAM,GAEjDa,EAAIvB,EAAOuC,GACff,EAAIxB,EAAOuC,EAAI,GACfd,EAAIzB,EAAOuC,EAAI,GACfb,EAAI1B,EAAOuC,EAAI,GAEXC,EAAMnC,EAAcK,EAC1ByB,IAAS1B,EAAMc,EAAIA,GAAKiB,EACxBJ,IAAS3B,EAAMe,EAAIA,GAAKgB,EACxBH,IAAS5B,EAAMgB,EAAIA,GAAKe,EACxBF,IAAS7B,EAAMiB,EAAIA,GAAKc,EAExBb,GAAUJ,EACVK,GAAUJ,EACVK,GAAUJ,EACVK,GAAUJ,EAEVjB,EAAQA,EAAME,KAGhBC,EAAUL,EACVM,EAAWd,MACN,IAAI0C,EAAI,EAAGA,EAAI3D,EAAO2D,IAAK,KACxBC,EAAaJ,EAAOtB,IAAYC,KACtCjB,EAAOe,EAAK,GAAK2B,EACC,IAAdA,EAAiB,KACbhB,EAAI,IAAMgB,EAChB1C,EAAOe,IAAQoB,EAAOnB,IAAYC,GAAUS,EAC5C1B,EAAOe,EAAK,IAAOqB,EAAOpB,IAAYC,GAAUS,EAChD1B,EAAOe,EAAK,IAAOsB,EAAOrB,IAAYC,GAAUS,OAEhD1B,EAAOe,GAAMf,EAAOe,EAAK,GAAKf,EAAOe,EAAK,GAAK,EAGjDoB,GAAQJ,EACRK,GAAQJ,EACRK,GAAQJ,EACRK,GAAQJ,EAERH,GAAWnB,EAAQW,EACnBS,GAAWpB,EAAQY,EACnBS,GAAWrB,EAAQa,EACnBS,GAAWtB,EAAQc,MAEfa,EAAIE,EAAI/C,EAAS,EACrB6C,EAAKzB,GAAMyB,EAAIpC,EACXoC,EACApC,IAAiB,EAOrBgC,GALAR,GAAWf,EAAQW,EAAIvB,EAAOuC,GAM9BH,GALAR,GAAWhB,EAAQY,EAAIxB,EAAOuC,EAAI,GAMlCF,GALAR,GAAWjB,EAAQa,EAAIzB,EAAOuC,EAAI,GAMlCD,GALAR,GAAWlB,EAAQc,EAAI1B,EAAOuC,EAAI,GAOlC3B,EAAUA,EAAQD,YAEGE,EAAdU,MAAAA,EAAGC,MAAAA,EAAGC,MAAAA,EAAGC,MAAAA,EAEhBK,GAAWR,GACXS,GAAWR,GACXS,GAAWR,GACXS,GAAWR,GAEXC,GAAUJ,GACVK,GAAUJ,GACVK,GAAUJ,GACVK,GAAUJ,GAEVb,EAAWA,EAASF,KAEpBI,GAAM,EAERD,GAAMhC,MAGH,IAAI2D,GAAI,EAAGA,GAAI3D,EAAO2D,KAAK,KAG1BtB,GAAKnB,EAFTe,EAAK0B,IAAK,GAGRrB,GAAKpB,EAAOe,EAAK,GACjBM,GAAKrB,EAAOe,EAAK,GACjBO,GAAKtB,EAAOe,EAAK,GACjBgB,GAAU1B,EAAcc,GACxBa,GAAU3B,EAAce,GACxBa,GAAU5B,EAAcgB,GACxBa,GAAU7B,EAAciB,GACxBa,GAAO7B,EAAYa,GACnBiB,GAAO9B,EAAYc,GACnBiB,GAAO/B,EAAYe,GACnBiB,GAAOhC,EAAYgB,GAErBb,EAAQF,MAEH,IAAIG,GAAI,EAAGA,GAAIL,EAAaK,KAC/BD,EAAMc,EAAIJ,GACVV,EAAMe,EAAIJ,GACVX,EAAMgB,EAAIJ,GACVZ,EAAMiB,EAAIJ,GACVb,EAAQA,EAAME,aAGZgC,GAAK7D,EAEL8C,GAAS,EAAGC,GAAS,EAAGC,GAAS,EAAGH,GAAS,EACxCjB,GAAI,EAAGA,IAAKhB,EAAQgB,KAAK,CAChCK,EAAM4B,GAAKF,IAAM,MAEXD,GAAMnC,EAAcK,GAC1ByB,KAAS1B,EAAMc,EAAKJ,GAAKnB,EAAOe,IAAQyB,GACxCJ,KAAS3B,EAAMe,EAAKJ,GAAKpB,EAAOe,EAAK,IAAOyB,GAC5CH,KAAS5B,EAAMgB,EAAKJ,GAAKrB,EAAOe,EAAK,IAAOyB,GAC5CF,KAAS7B,EAAMiB,EAAKJ,GAAKtB,EAAOe,EAAK,IAAOyB,GAE5Cb,IAAUR,GACVS,IAAUR,GACVS,IAAUR,GACVS,IAAUR,GAEVb,EAAQA,EAAME,KAEVD,GAAIN,IACNuC,IAAM7D,GAIViC,EAAK0B,GACL7B,EAAUL,EACVM,EAAWd,MACN,IAAImB,GAAI,EAAGA,GAAInC,EAAQmC,KAAK,KAC3BqB,GAAIxB,GAAM,EACdf,EAAOuC,GAAI,GAAKjB,GAAMgB,GAAOtB,IAAYC,EACrCK,GAAK,GACPA,GAAK,IAAMA,GACXtB,EAAOuC,KAAOJ,GAAOnB,IAAYC,GAAUK,GAC3CtB,EAAOuC,GAAI,IAAOH,GAAOpB,IAAYC,GAAUK,GAC/CtB,EAAOuC,GAAI,IAAOF,GAAOrB,IAAYC,GAAUK,IAE/CtB,EAAOuC,IAAKvC,EAAOuC,GAAI,GAAKvC,EAAOuC,GAAI,GAAK,EAG9CJ,IAAQJ,GACRK,IAAQJ,GACRK,IAAQJ,GACRK,IAAQJ,GAERH,IAAWnB,EAAQW,EACnBS,IAAWpB,EAAQY,EACnBS,IAAWrB,EAAQa,EACnBS,IAAWtB,EAAQc,EAEnBa,GAAKE,KACDF,GAAIrB,GAAIb,GAAeD,EAAemC,GAAInC,GACpCtB,GACJ,EAENqD,IAASR,IAAWf,EAAQW,EAAIvB,EAAOuC,IACvCH,IAASR,IAAWhB,EAAQY,EAAIxB,EAAOuC,GAAI,GAC3CF,IAASR,IAAWjB,EAAQa,EAAIzB,EAAOuC,GAAI,GAC3CD,IAASR,IAAWlB,EAAQc,EAAI1B,EAAOuC,GAAI,GAE3C3B,EAAUA,EAAQD,KAElBoB,IAAYZ,GAAKN,EAASU,EAC1BS,IAAYZ,GAAKP,EAASW,EAC1BS,IAAYZ,GAAKR,EAASY,EAC1BS,IAAYZ,GAAKT,EAASa,EAE1BC,IAAUR,GACVS,IAAUR,GACVS,IAAUR,GACVS,IAAUR,GAEVT,EAAWA,EAASF,KAEpBI,GAAMjC,UAGHc,EAYT,SAASgD,EAAkBjE,EAAQC,EAAMC,EAAMC,EAAOC,EAAQW,QACxDC,MAAMD,IAAWA,EAAS,IAC9BA,GAAU,MAENE,EAAYlB,EAAuBC,EAAQC,EAAMC,EAAMC,EAAOC,GAClEa,EAAYiD,EACVjD,EAAWhB,EAAMC,EAAMC,EAAOC,EAAQW,GAGxCf,EAAOU,WAAW,MAAMS,aAAaF,EAAWhB,EAAMC,IAYxD,SAASgE,EAAqBjD,EAAWhB,EAAMC,EAAMC,EAAOC,EAAQW,WAY9DK,EAXEC,EAASJ,EAAUK,KAEnBC,EAAM,EAAIR,EAAS,EAEnBS,EAAcrB,EAAQ,EACtBsB,EAAerB,EAAS,EACxBsB,EAAcX,EAAS,EACvBY,EAAYD,GAAeA,EAAc,GAAK,EAE9CE,EAAa,IAAIC,EACnBC,EAAQF,EAEHG,EAAI,EAAGA,EAAIR,EAAKQ,IACvBD,EAAQA,EAAME,KAAO,IAAIH,EACrBE,IAAML,IACRN,EAAWU,GAGfA,EAAME,KAAOJ,UAOTgC,EAAGC,EANH5B,EAAU,KACVC,EAAW,KAETG,EAASxC,EAASkB,GAClBuB,EAASxC,EAASiB,GAGpBoB,EAAK,EAAGC,EAAK,EAERG,EAAI,EAAGA,EAAInC,EAAQmC,IAAK,KAC3BC,EAAKnB,EAAOe,GACdK,EAAKpB,EAAOe,EAAK,GACjBM,EAAKrB,EAAOe,EAAK,GACjBgB,EAAU1B,EAAcc,EACxBa,EAAU3B,EAAce,EACxBa,EAAU5B,EAAcgB,EACxBc,EAAO7B,EAAYa,EACnBiB,EAAO9B,EAAYc,EACnBiB,EAAO/B,EAAYe,EAErBZ,EAAQF,MAEH,IAAIG,EAAI,EAAGA,EAAIL,EAAaK,IAC/BD,EAAMc,EAAIJ,EACVV,EAAMe,EAAIJ,EACVX,EAAMgB,EAAIJ,EACVZ,EAAQA,EAAME,aAGZgB,EAAS,EAAGC,EAAS,EAAGC,EAAS,EAC5BnB,EAAI,EAAGA,EAAIL,EAAaK,IAC/B6B,EAAIxB,IAAOZ,EAAcO,EAAIP,EAAcO,IAAM,GACjDyB,IAAS1B,EAAMc,EAAKJ,EAAKnB,EAAOuC,KAAQC,EAAMnC,EAAcK,GAC5D0B,IAAS3B,EAAMe,EAAKJ,EAAKpB,EAAOuC,EAAI,IAAOC,EAC3CH,IAAS5B,EAAMgB,EAAKJ,EAAKrB,EAAOuC,EAAI,IAAOC,EAE3Cb,GAAUR,EACVS,GAAUR,EACVS,GAAUR,EAEVZ,EAAQA,EAAME,KAGhBC,EAAUL,EACVM,EAAWd,MACN,IAAI0C,EAAI,EAAGA,EAAI3D,EAAO2D,IACzBzC,EAAOe,GAAOoB,EAAOnB,IAAYC,EACjCjB,EAAOe,EAAK,GAAMqB,EAAOpB,IAAYC,EACrCjB,EAAOe,EAAK,GAAMsB,EAAOrB,IAAYC,EAErCkB,GAAQJ,EACRK,GAAQJ,EACRK,GAAQJ,EAERF,GAAWnB,EAAQW,EACnBS,GAAWpB,EAAQY,EACnBS,GAAWrB,EAAQa,EAEnBc,EAAKzB,IACFyB,EAAIE,EAAI/C,EAAS,GAAKS,EAAcoC,EAAIpC,IACrC,EAMNgC,GAJAR,GAAWf,EAAQW,EAAIvB,EAAOuC,GAK9BH,GAJAR,GAAWhB,EAAQY,EAAIxB,EAAOuC,EAAI,GAKlCF,GAJAR,GAAWjB,EAAQa,EAAIzB,EAAOuC,EAAI,GAMlC3B,EAAUA,EAAQD,KAElBoB,GAAYZ,EAAKN,EAASU,EAC1BS,GAAYZ,EAAKP,EAASW,EAC1BS,GAAYZ,EAAKR,EAASY,EAE1BE,GAAUR,EACVS,GAAUR,EACVS,GAAUR,EAEVR,EAAWA,EAASF,KAEpBI,GAAM,EAERD,GAAMhC,MAGH,IAAI2D,EAAI,EAAGA,EAAI3D,EAAO2D,IAAK,KAE1BtB,EAAKnB,EADTe,EAAK0B,GAAK,GAERrB,EAAKpB,EAAOe,EAAK,GACjBM,EAAKrB,EAAOe,EAAK,GACjBgB,EAAU1B,EAAcc,EACxBa,EAAU3B,EAAce,EACxBa,EAAU5B,EAAcgB,EACxBc,EAAO7B,EAAYa,EACnBiB,EAAO9B,EAAYc,EACnBiB,EAAO/B,EAAYe,EAErBZ,EAAQF,MAEH,IAAIG,EAAI,EAAGA,EAAIL,EAAaK,IAC/BD,EAAMc,EAAIJ,EACVV,EAAMe,EAAIJ,EACVX,EAAMgB,EAAIJ,EACVZ,EAAQA,EAAME,aAGZgB,GAAS,EAAGC,GAAS,EAAGC,GAAS,EAC5BnB,GAAI,EAAGiC,GAAK7D,EAAO4B,IAAKhB,EAAQgB,KACvCK,EAAM4B,GAAKF,GAAM,EAEjBN,IAAS1B,EAAMc,EAAKJ,EAAKnB,EAAOe,KAASyB,EAAMnC,EAAcK,IAC7D0B,IAAS3B,EAAMe,EAAKJ,EAAKpB,EAAOe,EAAK,IAAOyB,EAC5CH,IAAS5B,EAAMgB,EAAKJ,EAAKrB,EAAOe,EAAK,IAAOyB,EAE5Cb,IAAUR,EACVS,IAAUR,EACVS,IAAUR,EAEVZ,EAAQA,EAAME,KAEVD,GAAIN,IACNuC,IAAM7D,GAIViC,EAAK0B,EACL7B,EAAUL,EACVM,EAAWd,MACN,IAAImB,GAAI,EAAGA,GAAInC,EAAQmC,KAE1BlB,EADAuC,EAAIxB,GAAM,GACGoB,EAAOnB,IAAYC,EAChCjB,EAAOuC,EAAI,GAAMH,EAAOpB,IAAYC,EACpCjB,EAAOuC,EAAI,GAAMF,EAAOrB,IAAYC,EAEpCkB,GAAQJ,EACRK,GAAQJ,EACRK,GAAQJ,EAERF,GAAWnB,EAAQW,EACnBS,GAAWpB,EAAQY,EACnBS,GAAWrB,EAAQa,EAEnBc,EAAKE,IACDF,EAAIrB,GAAIb,GAAeD,EAAemC,EAAInC,GACpCtB,GACJ,EAENqD,GAASR,IAAWf,EAAQW,EAAIvB,EAAOuC,GACvCH,GAASR,IAAWhB,EAAQY,EAAIxB,EAAOuC,EAAI,GAC3CF,GAASR,IAAWjB,EAAQa,EAAIzB,EAAOuC,EAAI,GAE3C3B,EAAUA,EAAQD,KAElBoB,GAAYZ,EAAKN,EAASU,EAC1BS,GAAYZ,EAAKP,EAASW,EAC1BS,GAAYZ,EAAKR,EAASY,EAE1BE,IAAUR,EACVS,IAAUR,EACVS,IAAUR,EAEVR,EAAWA,EAASF,KAEpBI,GAAMjC,SAIHc,MAMIY,EAIX,wHACOe,EAAI,OACJC,EAAI,OACJC,EAAI,OACJC,EAAI,OACJf,KAAO,yDA5kBhB,SACEmC,EAAKnE,EAAQe,EAAQqD,EAAkBC,EAAWC,MAE/B,iBAARH,IACTA,EAAM9D,SAASC,eAAe6D,IAI7BA,IAEC,qBADDI,OAAOC,UAAUC,SAASC,KAAKP,GAAKQ,MAAM,GAAI,IACrB,iBAAkBR,QAKxCS,EAAgBP,EAAY,SAAW,UACzCQ,EAAIV,EAAIS,EAAgB,SACxBE,EAAIX,EAAIS,EAAgB,aAG6B,gBAArDL,OAAOC,UAAUC,SAASC,KAAKP,GAAKQ,MAAM,GAAI,KAChDE,EAAIV,EAAIhE,MACR2E,EAAIX,EAAI/D,QAGY,iBAAXJ,IACTA,EAASK,SAASC,eAAeN,IAE9BA,GAAY,eAAgBA,GAI5BsE,IACHtE,EAAO+E,MAAM5E,MAAQ0E,EAAI,KACzB7E,EAAO+E,MAAM3E,OAAS0E,EAAI,MAE5B9E,EAAOG,MAAQ0E,EACf7E,EAAOI,OAAS0E,MAEVrE,EAAUT,EAAOU,WAAW,MAClCD,EAAQuE,UAAU,EAAG,EAAGH,EAAGC,GAC3BrE,EAAQwE,UAAUd,EAAK,EAAG,EAAGA,EAAIe,aAAcf,EAAIgB,cAAe,EAAG,EAAGN,EAAGC,GAEvE9D,MAAMD,IAAWA,EAAS,IAE1BqD,EACFtD,EAAkBd,EAAQ,EAAG,EAAG6E,EAAGC,EAAG/D,GAEtCkD,EAAiBjE,EAAQ,EAAG,EAAG6E,EAAGC,EAAG/D"}