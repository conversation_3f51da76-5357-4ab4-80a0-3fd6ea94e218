# 🚀 Deployment Guide - Roti Ragil Invoice dengan Database

## 📋 **Langkah-langkah Deploy ke Netlify dengan Database**

### **1. Setup Database PostgreSQL di Neon**

1. **Jalankan SQL Schema:**
   - Buka Neon dashboard Anda
   - Masuk ke SQL Editor
   - Copy dan jalankan semua kode dari file `database/schema.sql`
   - Ini akan membuat tabel: `products`, `customers`, `invoices`, `invoice_items`

### **2. Upload ke Netlify**

1. **Build aplikasi:**
   ```bash
   npm run build
   ```

2. **Upload ke Netlify:**
   - Drag & drop folder `dist` ke Netlify
   - Atau gunakan Netlify CLI:
     ```bash
     netlify deploy --prod --dir=dist
     ```

### **3. Setup Environment Variables di Netlify**

1. **Di Netlify Dashboard:**
   - Masuk ke Site Settings → Environment Variables
   - Tambahkan variable:
     - **Key:** `NETLIFY_DATABASE_URL`
     - **Value:** Connection string PostgreSQL Anda
     - Format: `postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require`

### **4. Deploy Netlify Functions**

1. **Upload Functions:**
   - Pastikan folder `netlify/functions/` ter-upload
   - Netlify akan otomatis detect dan deploy functions

2. **Test API Endpoints:**
   - `/.netlify/functions/db/products` (GET, POST, PUT, DELETE)
   - `/.netlify/functions/db/customers` (GET, POST, PUT, DELETE)
   - `/.netlify/functions/db/invoices` (GET, POST, PUT, DELETE)

---

## 🔧 **Fitur Database Integration**

### **✅ Yang Sudah Diimplementasi:**

1. **Hybrid Storage System:**
   - Online: Data tersimpan di PostgreSQL
   - Offline: Fallback ke LocalStorage
   - Auto-sync saat online kembali

2. **API Endpoints:**
   - Products CRUD
   - Customers CRUD  
   - Invoices CRUD dengan items

3. **Real-time Sync:**
   - Data otomatis sync antara devices
   - Cache di LocalStorage untuk performa

### **📱 Cara Kerja di PWA Android:**

1. **Saat Online:**
   - Data disimpan ke database PostgreSQL
   - Cache di LocalStorage untuk akses cepat

2. **Saat Offline:**
   - Menggunakan data dari LocalStorage
   - Operasi CRUD tetap bisa dilakukan
   - Data akan sync saat online kembali

---

## 🗄️ **Database Schema**

```sql
-- Products
CREATE TABLE products (
    id UUID PRIMARY KEY,
    nama VARCHAR(255),
    harga DECIMAL(10,2),
    deskripsi TEXT,
    created_at TIMESTAMP
);

-- Customers  
CREATE TABLE customers (
    id UUID PRIMARY KEY,
    nama VARCHAR(255),
    alamat TEXT,
    telepon VARCHAR(50),
    email VARCHAR(255),
    created_at TIMESTAMP
);

-- Invoices
CREATE TABLE invoices (
    id UUID PRIMARY KEY,
    nomor_invoice VARCHAR(100),
    customer_id UUID REFERENCES customers(id),
    tanggal DATE,
    total DECIMAL(10,2),
    status VARCHAR(20),
    created_at TIMESTAMP
);

-- Invoice Items
CREATE TABLE invoice_items (
    id UUID PRIMARY KEY,
    invoice_id UUID REFERENCES invoices(id),
    product_id UUID REFERENCES products(id),
    quantity INTEGER,
    harga DECIMAL(10,2)
);
```

---

## 🔄 **Testing Sync**

### **Test Scenario:**

1. **Buka aplikasi di browser desktop**
2. **Tambah produk/pelanggan/invoice**
3. **Buka aplikasi di PWA Android**
4. **Data harus sama di kedua device**

### **Test Offline Mode:**

1. **Matikan internet di Android**
2. **Tambah data baru**
3. **Nyalakan internet kembali**
4. **Data harus ter-sync**

---

## 📱 **Install PWA di Android**

1. **Buka URL Netlify di Chrome Android**
2. **Menu → "Add to Home screen"**
3. **Aplikasi ter-install sebagai native app**
4. **Data tersync dengan database cloud**

---

## 🛠️ **Troubleshooting**

### **Jika API tidak berfungsi:**
1. Cek Environment Variables di Netlify
2. Cek Functions logs di Netlify dashboard
3. Pastikan database connection string benar

### **Jika data tidak sync:**
1. Cek network connection
2. Buka Developer Tools → Network tab
3. Lihat API calls ke `/.netlify/functions/db`

---

## 🎯 **Hasil Akhir**

- ✅ **PWA Android** dengan database cloud
- ✅ **Real-time sync** antar devices  
- ✅ **Offline support** dengan LocalStorage fallback
- ✅ **Data backup** otomatis di PostgreSQL
- ✅ **Multi-device access** dengan data yang sama

**Sekarang aplikasi Roti Ragil bisa digunakan di Android dengan data yang tersinkronisasi dengan database Netlify!** 🎉
