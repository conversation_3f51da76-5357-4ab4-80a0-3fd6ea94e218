{"hash": "fbb5b458", "browserHash": "ef6e31c0", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "c28a3a71", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "88a95426", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "b17b0090", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "0e92ac9c", "needsInterop": true}, "jspdf": {"src": "../../jspdf/dist/jspdf.es.min.js", "file": "jspdf.js", "fileHash": "63f0810e", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "ee655a41", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "5ed79819", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "2f55b236", "needsInterop": false}}, "chunks": {"html2canvas.esm-HY3AASUL": {"file": "html2canvas__esm-HY3AASUL.js"}, "purify.es-CUTLTK2H": {"file": "purify__es-CUTLTK2H.js"}, "index.es-X2XKY4TU": {"file": "index__es-X2XKY4TU.js"}, "chunk-D7ZASVPN": {"file": "chunk-D7ZASVPN.js"}, "chunk-WALXKXZM": {"file": "chunk-WALXKXZM.js"}, "chunk-WQMOH32Y": {"file": "chunk-WQMOH32Y.js"}, "chunk-5WWUZCGV": {"file": "chunk-5WWUZCGV.js"}}}