const { Pool } = require('pg');

// Database connection
const pool = new Pool({
  connectionString: process.env.NETLIFY_DATABASE_URL,
  ssl: {
    rejectUnauthorized: false
  }
});

// CORS headers
const headers = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Content-Type': 'application/json'
};

exports.handler = async (event, context) => {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  const { httpMethod, path, body } = event;
  const segments = path.split('/').filter(Boolean);
  const resource = segments[segments.length - 2]; // products, customers, invoices
  const id = segments[segments.length - 1];

  try {
    let result;

    switch (resource) {
      case 'products':
        result = await handleProducts(httpMethod, id, body);
        break;
      case 'customers':
        result = await handleCustomers(httpMethod, id, body);
        break;
      case 'invoices':
        result = await handleInvoices(httpMethod, id, body);
        break;
      default:
        return {
          statusCode: 404,
          headers,
          body: JSON.stringify({ error: 'Resource not found' })
        };
    }

    return {
      statusCode: result.statusCode || 200,
      headers,
      body: JSON.stringify(result.data)
    };

  } catch (error) {
    console.error('Database error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Internal server error', details: error.message })
    };
  }
};

// Products handlers
async function handleProducts(method, id, body) {
  switch (method) {
    case 'GET':
      if (id && id !== 'products') {
        const result = await pool.query('SELECT * FROM products WHERE id = $1', [id]);
        return { data: result.rows[0] || null };
      } else {
        const result = await pool.query('SELECT * FROM products ORDER BY created_at DESC');
        return { data: result.rows };
      }

    case 'POST':
      const { nama, harga, deskripsi } = JSON.parse(body);
      const insertResult = await pool.query(
        'INSERT INTO products (nama, harga, deskripsi) VALUES ($1, $2, $3) RETURNING *',
        [nama, harga, deskripsi]
      );
      return { data: insertResult.rows[0] };

    case 'PUT':
      const updateData = JSON.parse(body);
      const updateResult = await pool.query(
        'UPDATE products SET nama = $1, harga = $2, deskripsi = $3, updated_at = NOW() WHERE id = $4 RETURNING *',
        [updateData.nama, updateData.harga, updateData.deskripsi, id]
      );
      return { data: updateResult.rows[0] };

    case 'DELETE':
      await pool.query('DELETE FROM products WHERE id = $1', [id]);
      return { data: { success: true } };

    default:
      return { statusCode: 405, data: { error: 'Method not allowed' } };
  }
}

// Customers handlers
async function handleCustomers(method, id, body) {
  switch (method) {
    case 'GET':
      if (id && id !== 'customers') {
        const result = await pool.query('SELECT * FROM customers WHERE id = $1', [id]);
        return { data: result.rows[0] || null };
      } else {
        const result = await pool.query('SELECT * FROM customers ORDER BY created_at DESC');
        return { data: result.rows };
      }

    case 'POST':
      const { nama, alamat, telepon, email } = JSON.parse(body);
      const insertResult = await pool.query(
        'INSERT INTO customers (nama, alamat, telepon, email) VALUES ($1, $2, $3, $4) RETURNING *',
        [nama, alamat, telepon, email]
      );
      return { data: insertResult.rows[0] };

    case 'PUT':
      const updateData = JSON.parse(body);
      const updateResult = await pool.query(
        'UPDATE customers SET nama = $1, alamat = $2, telepon = $3, email = $4, updated_at = NOW() WHERE id = $5 RETURNING *',
        [updateData.nama, updateData.alamat, updateData.telepon, updateData.email, id]
      );
      return { data: updateResult.rows[0] };

    case 'DELETE':
      await pool.query('DELETE FROM customers WHERE id = $1', [id]);
      return { data: { success: true } };

    default:
      return { statusCode: 405, data: { error: 'Method not allowed' } };
  }
}

// Invoices handlers
async function handleInvoices(method, id, body) {
  switch (method) {
    case 'GET':
      if (id && id !== 'invoices') {
        // Get invoice with items
        const invoiceResult = await pool.query(`
          SELECT i.*, c.nama as customer_name 
          FROM invoices i 
          JOIN customers c ON i.customer_id = c.id 
          WHERE i.id = $1
        `, [id]);
        
        if (invoiceResult.rows.length === 0) {
          return { data: null };
        }

        const itemsResult = await pool.query(`
          SELECT ii.*, p.nama as product_name 
          FROM invoice_items ii 
          JOIN products p ON ii.product_id = p.id 
          WHERE ii.invoice_id = $1
        `, [id]);

        const invoice = invoiceResult.rows[0];
        invoice.items = itemsResult.rows;
        return { data: invoice };
      } else {
        const result = await pool.query(`
          SELECT i.*, c.nama as customer_name 
          FROM invoices i 
          JOIN customers c ON i.customer_id = c.id 
          ORDER BY i.created_at DESC
        `);
        return { data: result.rows };
      }

    case 'POST':
      const { customer_id, tanggal, items, status = 'draft' } = JSON.parse(body);
      
      // Generate invoice number
      const invoiceCount = await pool.query('SELECT COUNT(*) FROM invoices');
      const count = parseInt(invoiceCount.rows[0].count) + 1;
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const nomor_invoice = `INV/${year}${month}/${String(count).padStart(4, '0')}`;

      // Calculate total
      const total = items.reduce((sum, item) => sum + (item.quantity * item.harga), 0);

      // Insert invoice
      const invoiceResult = await pool.query(
        'INSERT INTO invoices (nomor_invoice, customer_id, tanggal, total, status) VALUES ($1, $2, $3, $4, $5) RETURNING *',
        [nomor_invoice, customer_id, tanggal, total, status]
      );

      const invoice = invoiceResult.rows[0];

      // Insert invoice items
      for (const item of items) {
        await pool.query(
          'INSERT INTO invoice_items (invoice_id, product_id, quantity, harga) VALUES ($1, $2, $3, $4)',
          [invoice.id, item.product_id, item.quantity, item.harga]
        );
      }

      return { data: invoice };

    case 'PUT':
      const updateData = JSON.parse(body);
      const updateResult = await pool.query(
        'UPDATE invoices SET status = $1, updated_at = NOW() WHERE id = $2 RETURNING *',
        [updateData.status, id]
      );
      return { data: updateResult.rows[0] };

    case 'DELETE':
      await pool.query('DELETE FROM invoices WHERE id = $1', [id]);
      return { data: { success: true } };

    default:
      return { statusCode: 405, data: { error: 'Method not allowed' } };
  }
}
