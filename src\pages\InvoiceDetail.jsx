import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { ArrowLeft, Download, Edit } from 'lucide-react'
import { getInvoiceById, getCustomers, getProducts } from '../services/storage'
import { generatePDF } from '../services/pdfService'

const InvoiceDetail = () => {
  const { id } = useParams()
  const navigate = useNavigate()
  const [invoice, setInvoice] = useState(null)
  const [customer, setCustomer] = useState(null)
  const [products, setProducts] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadInvoiceDetail()
  }, [id])

  const loadInvoiceDetail = () => {
    const invoiceData = getInvoiceById(id)
    const customersData = getCustomers()
    const productsData = getProducts()
    
    if (invoiceData) {
      setInvoice(invoiceData)
      const customerData = customersData.find(c => c.id === invoiceData.pelangganId)
      setCustomer(customerData)
      setProducts(productsData)
    }
    
    setLoading(false)
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR'
    }).format(amount)
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800'
      case 'sent':
        return 'bg-blue-100 text-blue-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status) => {
    switch (status) {
      case 'paid':
        return 'Lunas'
      case 'sent':
        return 'Terkirim'
      default:
        return 'Draft'
    }
  }

  const getProductName = (productId) => {
    const product = products.find(p => p.id === productId)
    return product ? product.nama : 'Produk Tidak Ditemukan'
  }

  const handleDownloadPDF = async () => {
    if (invoice && customer) {
      await generatePDF(invoice, customer)
    }
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!invoice) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-medium text-gray-900">Invoice tidak ditemukan</h3>
        <p className="mt-1 text-sm text-gray-500">
          Invoice yang Anda cari tidak ada atau telah dihapus.
        </p>
        <div className="mt-6">
          <button
            onClick={() => navigate('/invoices')}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Kembali ke Daftar Invoice
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <button
          onClick={() => navigate('/invoices')}
          className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700"
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          Kembali ke Daftar Invoice
        </button>
        <div className="flex space-x-3">
          <button
            onClick={handleDownloadPDF}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            <Download className="h-4 w-4 mr-2" />
            Download PDF
          </button>
        </div>
      </div>

      {/* Invoice Detail */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        {/* Header Section */}
        <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
          <div className="flex justify-between items-start">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">ROTI RAGIL</h1>
              <p className="text-sm text-gray-600">Toko Roti & Kue Terpercaya</p>
              <p className="text-sm text-gray-600">Telp: 0895402652626</p>
            </div>
            <div className="text-right">
              <h2 className="text-xl font-bold text-gray-900">INVOICE</h2>
              <p className="text-sm text-gray-600">No: {invoice.nomorInvoice}</p>
              <p className="text-sm text-gray-600">Tanggal: {formatDate(invoice.tanggal)}</p>
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(invoice.status)}`}>
                {getStatusText(invoice.status)}
              </span>
            </div>
          </div>
        </div>

        {/* Customer Info */}
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-2">Kepada:</h3>
          {customer ? (
            <div className="text-sm text-gray-600">
              <p className="font-medium text-gray-900">{customer.nama}</p>
              <p>{customer.alamat}</p>
              <p>Telp: {customer.telepon}</p>
              {customer.email && <p>Email: {customer.email}</p>}
            </div>
          ) : (
            <p className="text-sm text-gray-500">Data pelanggan tidak ditemukan</p>
          )}
        </div>

        {/* Items Table */}
        <div className="px-6 py-4">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    No
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Produk
                  </th>
                  <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Qty
                  </th>
                  <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Harga
                  </th>
                  <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Total
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {invoice.items.map((item, index) => (
                  <tr key={index}>
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                      {index + 1}
                    </td>
                    <td className="px-4 py-4 text-sm text-gray-900">
                      {getProductName(item.produkId)}
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900 text-center">
                      {item.quantity}
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                      {formatCurrency(item.harga)}
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                      {formatCurrency(item.quantity * item.harga)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Total Section */}
        <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
          <div className="flex justify-end">
            <div className="text-right">
              <p className="text-lg font-bold text-gray-900">
                TOTAL: {formatCurrency(invoice.total)}
              </p>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t border-gray-200">
          <div className="text-sm text-gray-600">
            <p className="font-medium">Terima kasih atas kepercayaan Anda!</p>
            <p>Pembayaran dapat dilakukan melalui transfer bank atau tunai.</p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default InvoiceDetail
