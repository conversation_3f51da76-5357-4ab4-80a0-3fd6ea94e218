// Invoice API functions
const API_BASE_URL = '/.netlify/functions/db'

// Check if we're online
const isOnline = () => navigator.onLine

// API helper function
const apiCall = async (endpoint, options = {}) => {
  try {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    })
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    return await response.json()
  } catch (error) {
    console.error('API call failed:', error)
    throw error
  }
}

// LocalStorage helpers
const getFromStorage = (key) => {
  try {
    const data = localStorage.getItem(key)
    return data ? JSON.parse(data) : []
  } catch (error) {
    console.error('Error reading from storage:', error)
    return []
  }
}

const saveToStorage = (key, data) => {
  try {
    localStorage.setItem(key, JSON.stringify(data))
    return true
  } catch (error) {
    console.error('Error saving to storage:', error)
    return false
  }
}

const STORAGE_KEYS = {
  INVOICES: 'roti_ragil_invoices'
}

// Invoice functions
export const getInvoices = async () => {
  try {
    if (isOnline()) {
      const data = await apiCall('/invoices')
      // Transform data to match frontend format
      const transformedData = data.map(invoice => ({
        id: invoice.id,
        nomorInvoice: invoice.nomor_invoice,
        pelangganId: invoice.customer_id,
        customerName: invoice.customer_name,
        tanggal: invoice.tanggal,
        total: parseFloat(invoice.total),
        status: invoice.status,
        createdAt: invoice.created_at
      }))
      saveToStorage(STORAGE_KEYS.INVOICES, transformedData)
      return transformedData
    } else {
      return getFromStorage(STORAGE_KEYS.INVOICES)
    }
  } catch (error) {
    console.error('Failed to fetch invoices:', error)
    return getFromStorage(STORAGE_KEYS.INVOICES)
  }
}

export const saveInvoice = async (invoice) => {
  try {
    if (isOnline()) {
      // Transform data to match API format
      const apiData = {
        customer_id: invoice.pelangganId,
        tanggal: invoice.tanggal,
        status: invoice.status,
        items: invoice.items.map(item => ({
          product_id: item.produkId,
          quantity: item.quantity,
          harga: item.harga
        }))
      }
      
      const newInvoice = await apiCall('/invoices', {
        method: 'POST',
        body: JSON.stringify(apiData)
      })
      
      // Transform response to frontend format
      const transformedInvoice = {
        id: newInvoice.id,
        nomorInvoice: newInvoice.nomor_invoice,
        pelangganId: newInvoice.customer_id,
        tanggal: newInvoice.tanggal,
        total: parseFloat(newInvoice.total),
        status: newInvoice.status,
        createdAt: newInvoice.created_at,
        items: invoice.items // Keep original items format for frontend
      }
      
      // Update localStorage cache
      const invoices = getFromStorage(STORAGE_KEYS.INVOICES)
      invoices.push(transformedInvoice)
      saveToStorage(STORAGE_KEYS.INVOICES, invoices)
      
      return transformedInvoice
    } else {
      // Fallback to localStorage when offline
      const invoices = getFromStorage(STORAGE_KEYS.INVOICES)
      const newInvoice = {
        id: Date.now().toString(),
        nomorInvoice: generateInvoiceNumber(),
        ...invoice,
        createdAt: new Date().toISOString(),
        _offline: true
      }
      invoices.push(newInvoice)
      saveToStorage(STORAGE_KEYS.INVOICES, invoices)
      return newInvoice
    }
  } catch (error) {
    console.error('Failed to save invoice:', error)
    throw error
  }
}

export const updateInvoice = async (id, updatedInvoice) => {
  try {
    if (isOnline()) {
      const updated = await apiCall(`/invoices/${id}`, {
        method: 'PUT',
        body: JSON.stringify(updatedInvoice)
      })
      
      // Update localStorage cache
      const invoices = getFromStorage(STORAGE_KEYS.INVOICES)
      const index = invoices.findIndex(i => i.id === id)
      if (index !== -1) {
        invoices[index] = { ...invoices[index], ...updatedInvoice }
        saveToStorage(STORAGE_KEYS.INVOICES, invoices)
      }
      return updated
    } else {
      const invoices = getFromStorage(STORAGE_KEYS.INVOICES)
      const index = invoices.findIndex(i => i.id === id)
      if (index !== -1) {
        invoices[index] = { ...invoices[index], ...updatedInvoice, _offline: true }
        saveToStorage(STORAGE_KEYS.INVOICES, invoices)
        return invoices[index]
      }
      return null
    }
  } catch (error) {
    console.error('Failed to update invoice:', error)
    throw error
  }
}

export const deleteInvoice = async (id) => {
  try {
    if (isOnline()) {
      await apiCall(`/invoices/${id}`, {
        method: 'DELETE'
      })
      const invoices = getFromStorage(STORAGE_KEYS.INVOICES)
      const filteredInvoices = invoices.filter(i => i.id !== id)
      saveToStorage(STORAGE_KEYS.INVOICES, filteredInvoices)
      return true
    } else {
      const invoices = getFromStorage(STORAGE_KEYS.INVOICES)
      const filteredInvoices = invoices.filter(i => i.id !== id)
      saveToStorage(STORAGE_KEYS.INVOICES, filteredInvoices)
      return true
    }
  } catch (error) {
    console.error('Failed to delete invoice:', error)
    throw error
  }
}

export const getInvoiceById = async (id) => {
  try {
    if (isOnline()) {
      const invoice = await apiCall(`/invoices/${id}`)
      if (invoice) {
        // Transform data to match frontend format
        const transformedInvoice = {
          id: invoice.id,
          nomorInvoice: invoice.nomor_invoice,
          pelangganId: invoice.customer_id,
          customerName: invoice.customer_name,
          tanggal: invoice.tanggal,
          total: parseFloat(invoice.total),
          status: invoice.status,
          createdAt: invoice.created_at,
          items: invoice.items ? invoice.items.map(item => ({
            produkId: item.product_id,
            productName: item.product_name,
            quantity: item.quantity,
            harga: parseFloat(item.harga)
          })) : []
        }
        return transformedInvoice
      }
      return null
    } else {
      const invoices = getFromStorage(STORAGE_KEYS.INVOICES)
      return invoices.find(i => i.id === id)
    }
  } catch (error) {
    console.error('Failed to fetch invoice:', error)
    const invoices = getFromStorage(STORAGE_KEYS.INVOICES)
    return invoices.find(i => i.id === id)
  }
}

// Helper functions
const generateInvoiceNumber = () => {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const invoices = getFromStorage(STORAGE_KEYS.INVOICES)
  const invoiceCount = invoices.length + 1
  return `INV/${year}${month}/${String(invoiceCount).padStart(4, '0')}`
}
