// Storage service untuk mengelola data via API dan localStorage sebagai fallback

const API_BASE_URL = '/.netlify/functions/db'

const STORAGE_KEYS = {
  PRODUCTS: 'roti_ragil_products',
  CUSTOMERS: 'roti_ragil_customers',
  INVOICES: 'roti_ragil_invoices',
  SETTINGS: 'roti_ragil_settings'
}

// Check if we're online and API is available
const isOnline = () => navigator.onLine && window.location.hostname !== 'localhost'

// API helper function with better error handling
const apiCall = async (endpoint, options = {}) => {
  try {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    return await response.json()
  } catch (error) {
    console.error('API call failed:', error)
    // If API fails, we'll fallback to localStorage
    throw error
  }
}

// Check if API is available
const checkApiAvailability = async () => {
  try {
    await fetch(`${API_BASE_URL}/products`, { method: 'HEAD' })
    return true
  } catch (error) {
    console.log('API not available, using localStorage fallback')
    return false
  }
}

// Generic storage functions
const getFromStorage = (key) => {
  try {
    const data = localStorage.getItem(key)
    return data ? JSON.parse(data) : []
  } catch (error) {
    console.error('Error reading from storage:', error)
    return []
  }
}

const saveToStorage = (key, data) => {
  try {
    localStorage.setItem(key, JSON.stringify(data))
    return true
  } catch (error) {
    console.error('Error saving to storage:', error)
    return false
  }
}

// Product functions
export const getProducts = async () => {
  try {
    if (isOnline()) {
      const data = await apiCall('/products')
      // Cache to localStorage
      saveToStorage(STORAGE_KEYS.PRODUCTS, data)
      return data
    } else {
      // Fallback to localStorage when offline
      return getFromStorage(STORAGE_KEYS.PRODUCTS)
    }
  } catch (error) {
    console.error('Failed to fetch products:', error)
    return getFromStorage(STORAGE_KEYS.PRODUCTS)
  }
}

export const saveProduct = async (product) => {
  try {
    if (isOnline()) {
      const newProduct = await apiCall('/products', {
        method: 'POST',
        body: JSON.stringify(product)
      })
      // Update localStorage cache
      const products = getFromStorage(STORAGE_KEYS.PRODUCTS)
      products.push(newProduct)
      saveToStorage(STORAGE_KEYS.PRODUCTS, products)
      return newProduct
    } else {
      // Fallback to localStorage when offline
      const products = getFromStorage(STORAGE_KEYS.PRODUCTS)
      const newProduct = {
        id: Date.now().toString(),
        ...product,
        created_at: new Date().toISOString(),
        _offline: true // Mark as offline data
      }
      products.push(newProduct)
      saveToStorage(STORAGE_KEYS.PRODUCTS, products)
      return newProduct
    }
  } catch (error) {
    console.error('Failed to save product:', error)
    throw error
  }
}

export const updateProduct = async (id, updatedProduct) => {
  try {
    if (isOnline()) {
      const updated = await apiCall(`/products/${id}`, {
        method: 'PUT',
        body: JSON.stringify(updatedProduct)
      })
      // Update localStorage cache
      const products = getFromStorage(STORAGE_KEYS.PRODUCTS)
      const index = products.findIndex(p => p.id === id)
      if (index !== -1) {
        products[index] = updated
        saveToStorage(STORAGE_KEYS.PRODUCTS, products)
      }
      return updated
    } else {
      // Fallback to localStorage when offline
      const products = getFromStorage(STORAGE_KEYS.PRODUCTS)
      const index = products.findIndex(p => p.id === id)
      if (index !== -1) {
        products[index] = { ...products[index], ...updatedProduct, _offline: true }
        saveToStorage(STORAGE_KEYS.PRODUCTS, products)
        return products[index]
      }
      return null
    }
  } catch (error) {
    console.error('Failed to update product:', error)
    throw error
  }
}

export const deleteProduct = async (id) => {
  try {
    if (isOnline()) {
      await apiCall(`/products/${id}`, {
        method: 'DELETE'
      })
      // Update localStorage cache
      const products = getFromStorage(STORAGE_KEYS.PRODUCTS)
      const filteredProducts = products.filter(p => p.id !== id)
      saveToStorage(STORAGE_KEYS.PRODUCTS, filteredProducts)
      return true
    } else {
      // Fallback to localStorage when offline
      const products = getFromStorage(STORAGE_KEYS.PRODUCTS)
      const filteredProducts = products.filter(p => p.id !== id)
      saveToStorage(STORAGE_KEYS.PRODUCTS, filteredProducts)
      return true
    }
  } catch (error) {
    console.error('Failed to delete product:', error)
    throw error
  }
}

// Customer functions
export const getCustomers = async () => {
  try {
    if (isOnline()) {
      const data = await apiCall('/customers')
      saveToStorage(STORAGE_KEYS.CUSTOMERS, data)
      return data
    } else {
      return getFromStorage(STORAGE_KEYS.CUSTOMERS)
    }
  } catch (error) {
    console.error('Failed to fetch customers:', error)
    return getFromStorage(STORAGE_KEYS.CUSTOMERS)
  }
}

export const saveCustomer = async (customer) => {
  try {
    if (isOnline()) {
      const newCustomer = await apiCall('/customers', {
        method: 'POST',
        body: JSON.stringify(customer)
      })
      const customers = getFromStorage(STORAGE_KEYS.CUSTOMERS)
      customers.push(newCustomer)
      saveToStorage(STORAGE_KEYS.CUSTOMERS, customers)
      return newCustomer
    } else {
      const customers = getFromStorage(STORAGE_KEYS.CUSTOMERS)
      const newCustomer = {
        id: Date.now().toString(),
        ...customer,
        created_at: new Date().toISOString(),
        _offline: true
      }
      customers.push(newCustomer)
      saveToStorage(STORAGE_KEYS.CUSTOMERS, customers)
      return newCustomer
    }
  } catch (error) {
    console.error('Failed to save customer:', error)
    throw error
  }
}

export const updateCustomer = async (id, updatedCustomer) => {
  try {
    if (isOnline()) {
      const updated = await apiCall(`/customers/${id}`, {
        method: 'PUT',
        body: JSON.stringify(updatedCustomer)
      })
      const customers = getFromStorage(STORAGE_KEYS.CUSTOMERS)
      const index = customers.findIndex(c => c.id === id)
      if (index !== -1) {
        customers[index] = updated
        saveToStorage(STORAGE_KEYS.CUSTOMERS, customers)
      }
      return updated
    } else {
      const customers = getFromStorage(STORAGE_KEYS.CUSTOMERS)
      const index = customers.findIndex(c => c.id === id)
      if (index !== -1) {
        customers[index] = { ...customers[index], ...updatedCustomer, _offline: true }
        saveToStorage(STORAGE_KEYS.CUSTOMERS, customers)
        return customers[index]
      }
      return null
    }
  } catch (error) {
    console.error('Failed to update customer:', error)
    throw error
  }
}

export const deleteCustomer = async (id) => {
  try {
    if (isOnline()) {
      await apiCall(`/customers/${id}`, {
        method: 'DELETE'
      })
      const customers = getFromStorage(STORAGE_KEYS.CUSTOMERS)
      const filteredCustomers = customers.filter(c => c.id !== id)
      saveToStorage(STORAGE_KEYS.CUSTOMERS, filteredCustomers)
      return true
    } else {
      const customers = getFromStorage(STORAGE_KEYS.CUSTOMERS)
      const filteredCustomers = customers.filter(c => c.id !== id)
      saveToStorage(STORAGE_KEYS.CUSTOMERS, filteredCustomers)
      return true
    }
  } catch (error) {
    console.error('Failed to delete customer:', error)
    throw error
  }
}

// Re-export invoice functions from separate API module
export {
  getInvoices,
  saveInvoice,
  updateInvoice,
  deleteInvoice,
  getInvoiceById
} from './invoiceApi.js'

// Helper functions
const generateInvoiceNumber = () => {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const invoices = getInvoices()
  const invoiceCount = invoices.length + 1
  return `INV/${year}${month}/${String(invoiceCount).padStart(4, '0')}`
}

// Initialize with sample data if empty
export const initializeSampleData = () => {
  if (getProducts().length === 0) {
    const sampleProducts = [
      { nama: 'Roti Tawar', harga: 15000, deskripsi: 'Roti tawar segar' },
      { nama: 'Roti Manis', harga: 12000, deskripsi: 'Roti manis lembut' },
      { nama: 'Croissant', harga: 18000, deskripsi: 'Croissant butter' }
    ]
    sampleProducts.forEach(product => saveProduct(product))
  }

  if (getCustomers().length === 0) {
    const sampleCustomers = [
      { nama: 'Toko Sari Roti', alamat: 'Jl. Merdeka No. 123', telepon: '081234567890', email: '<EMAIL>' },
      { nama: 'Warung Bu Ina', alamat: 'Jl. Sudirman No. 45', telepon: '081234567891', email: '<EMAIL>' }
    ]
    sampleCustomers.forEach(customer => saveCustomer(customer))
  }
}
