{"version": 3, "file": "purify.es.js", "sources": ["../src/utils.js", "../src/tags.js", "../src/attrs.js", "../src/regexp.js", "../src/purify.js"], "sourcesContent": ["const {\n  hasOwnProperty,\n  setPrototypeOf,\n  isFrozen,\n  getPrototypeOf,\n  getOwnPropertyDescriptor,\n} = Object;\n\nlet { freeze, seal, create } = Object; // eslint-disable-line import/no-mutable-exports\nlet { apply, construct } = typeof Reflect !== 'undefined' && Reflect;\n\nif (!apply) {\n  apply = function (fun, thisValue, args) {\n    return fun.apply(thisValue, args);\n  };\n}\n\nif (!freeze) {\n  freeze = function (x) {\n    return x;\n  };\n}\n\nif (!seal) {\n  seal = function (x) {\n    return x;\n  };\n}\n\nif (!construct) {\n  construct = function (Func, args) {\n    return new Func(...args);\n  };\n}\n\nconst arrayForEach = unapply(Array.prototype.forEach);\nconst arrayIndexOf = unapply(Array.prototype.indexOf);\nconst arrayPop = unapply(Array.prototype.pop);\nconst arrayPush = unapply(Array.prototype.push);\nconst arraySlice = unapply(Array.prototype.slice);\n\nconst stringToLowerCase = unapply(String.prototype.toLowerCase);\nconst stringToString = unapply(String.prototype.toString);\nconst stringMatch = unapply(String.prototype.match);\nconst stringReplace = unapply(String.prototype.replace);\nconst stringIndexOf = unapply(String.prototype.indexOf);\nconst stringTrim = unapply(String.prototype.trim);\n\nconst regExpTest = unapply(RegExp.prototype.test);\n\nconst typeErrorCreate = unconstruct(TypeError);\n\nexport function unapply(func) {\n  return (thisArg, ...args) => apply(func, thisArg, args);\n}\n\nexport function unconstruct(func) {\n  return (...args) => construct(func, args);\n}\n\n/* Add properties to a lookup table */\nexport function addToSet(set, array, transformCaseFunc) {\n  transformCaseFunc = transformCaseFunc ?? stringToLowerCase;\n  if (setPrototypeOf) {\n    // Make 'in' and truthy checks like Boolean(set.constructor)\n    // independent of any properties defined on Object.prototype.\n    // Prevent prototype setters from intercepting set as a this value.\n    setPrototypeOf(set, null);\n  }\n\n  let l = array.length;\n  while (l--) {\n    let element = array[l];\n    if (typeof element === 'string') {\n      const lcElement = transformCaseFunc(element);\n      if (lcElement !== element) {\n        // Config presets (e.g. tags.js, attrs.js) are immutable.\n        if (!isFrozen(array)) {\n          array[l] = lcElement;\n        }\n\n        element = lcElement;\n      }\n    }\n\n    set[element] = true;\n  }\n\n  return set;\n}\n\n/* Shallow clone an object */\nexport function clone(object) {\n  const newObject = create(null);\n\n  let property;\n  for (property in object) {\n    if (apply(hasOwnProperty, object, [property]) === true) {\n      newObject[property] = object[property];\n    }\n  }\n\n  return newObject;\n}\n\n/* IE10 doesn't support __lookupGetter__ so lets'\n * simulate it. It also automatically checks\n * if the prop is function or getter and behaves\n * accordingly. */\nfunction lookupGetter(object, prop) {\n  while (object !== null) {\n    const desc = getOwnPropertyDescriptor(object, prop);\n    if (desc) {\n      if (desc.get) {\n        return unapply(desc.get);\n      }\n\n      if (typeof desc.value === 'function') {\n        return unapply(desc.value);\n      }\n    }\n\n    object = getPrototypeOf(object);\n  }\n\n  function fallbackValue(element) {\n    console.warn('fallback value for', element);\n    return null;\n  }\n\n  return fallbackValue;\n}\n\nexport {\n  // Array\n  arrayForEach,\n  arrayIndexOf,\n  arrayPop,\n  arrayPush,\n  arraySlice,\n  // Object\n  freeze,\n  getPrototypeOf,\n  getOwnPropertyDescriptor,\n  hasOwnProperty,\n  isFrozen,\n  setPrototypeOf,\n  seal,\n  // RegExp\n  regExpTest,\n  // String\n  stringIndexOf,\n  stringMatch,\n  stringReplace,\n  stringToLowerCase,\n  stringToString,\n  stringTrim,\n  // Errors\n  typeErrorCreate,\n  // Other\n  lookupGetter,\n};\n", "import { freeze } from './utils.js';\n\nexport const html = freeze([\n  'a',\n  'abbr',\n  'acronym',\n  'address',\n  'area',\n  'article',\n  'aside',\n  'audio',\n  'b',\n  'bdi',\n  'bdo',\n  'big',\n  'blink',\n  'blockquote',\n  'body',\n  'br',\n  'button',\n  'canvas',\n  'caption',\n  'center',\n  'cite',\n  'code',\n  'col',\n  'colgroup',\n  'content',\n  'data',\n  'datalist',\n  'dd',\n  'decorator',\n  'del',\n  'details',\n  'dfn',\n  'dialog',\n  'dir',\n  'div',\n  'dl',\n  'dt',\n  'element',\n  'em',\n  'fieldset',\n  'figcaption',\n  'figure',\n  'font',\n  'footer',\n  'form',\n  'h1',\n  'h2',\n  'h3',\n  'h4',\n  'h5',\n  'h6',\n  'head',\n  'header',\n  'hgroup',\n  'hr',\n  'html',\n  'i',\n  'img',\n  'input',\n  'ins',\n  'kbd',\n  'label',\n  'legend',\n  'li',\n  'main',\n  'map',\n  'mark',\n  'marquee',\n  'menu',\n  'menuitem',\n  'meter',\n  'nav',\n  'nobr',\n  'ol',\n  'optgroup',\n  'option',\n  'output',\n  'p',\n  'picture',\n  'pre',\n  'progress',\n  'q',\n  'rp',\n  'rt',\n  'ruby',\n  's',\n  'samp',\n  'section',\n  'select',\n  'shadow',\n  'small',\n  'source',\n  'spacer',\n  'span',\n  'strike',\n  'strong',\n  'style',\n  'sub',\n  'summary',\n  'sup',\n  'table',\n  'tbody',\n  'td',\n  'template',\n  'textarea',\n  'tfoot',\n  'th',\n  'thead',\n  'time',\n  'tr',\n  'track',\n  'tt',\n  'u',\n  'ul',\n  'var',\n  'video',\n  'wbr',\n]);\n\n// SVG\nexport const svg = freeze([\n  'svg',\n  'a',\n  'altglyph',\n  'altglyphdef',\n  'altglyphitem',\n  'animatecolor',\n  'animatemotion',\n  'animatetransform',\n  'circle',\n  'clippath',\n  'defs',\n  'desc',\n  'ellipse',\n  'filter',\n  'font',\n  'g',\n  'glyph',\n  'glyphref',\n  'hkern',\n  'image',\n  'line',\n  'lineargradient',\n  'marker',\n  'mask',\n  'metadata',\n  'mpath',\n  'path',\n  'pattern',\n  'polygon',\n  'polyline',\n  'radialgradient',\n  'rect',\n  'stop',\n  'style',\n  'switch',\n  'symbol',\n  'text',\n  'textpath',\n  'title',\n  'tref',\n  'tspan',\n  'view',\n  'vkern',\n]);\n\nexport const svgFilters = freeze([\n  'feBlend',\n  'feColorMatrix',\n  'feComponentTransfer',\n  'feComposite',\n  'feConvolveMatrix',\n  'feDiffuseLighting',\n  'feDisplacementMap',\n  'feDistantLight',\n  'feFlood',\n  'feFuncA',\n  'feFuncB',\n  'feFuncG',\n  'feFuncR',\n  'feGaussianBlur',\n  'feImage',\n  'feMerge',\n  'feMergeNode',\n  'feMorphology',\n  'feOffset',\n  'fePointLight',\n  'feSpecularLighting',\n  'feSpotLight',\n  'feTile',\n  'feTurbulence',\n]);\n\n// List of SVG elements that are disallowed by default.\n// We still need to know them so that we can do namespace\n// checks properly in case one wants to add them to\n// allow-list.\nexport const svgDisallowed = freeze([\n  'animate',\n  'color-profile',\n  'cursor',\n  'discard',\n  'fedropshadow',\n  'font-face',\n  'font-face-format',\n  'font-face-name',\n  'font-face-src',\n  'font-face-uri',\n  'foreignobject',\n  'hatch',\n  'hatchpath',\n  'mesh',\n  'meshgradient',\n  'meshpatch',\n  'meshrow',\n  'missing-glyph',\n  'script',\n  'set',\n  'solidcolor',\n  'unknown',\n  'use',\n]);\n\nexport const mathMl = freeze([\n  'math',\n  'menclose',\n  'merror',\n  'mfenced',\n  'mfrac',\n  'mglyph',\n  'mi',\n  'mlabeledtr',\n  'mmultiscripts',\n  'mn',\n  'mo',\n  'mover',\n  'mpadded',\n  'mphantom',\n  'mroot',\n  'mrow',\n  'ms',\n  'mspace',\n  'msqrt',\n  'mstyle',\n  'msub',\n  'msup',\n  'msubsup',\n  'mtable',\n  'mtd',\n  'mtext',\n  'mtr',\n  'munder',\n  'munderover',\n]);\n\n// Similarly to SVG, we want to know all MathML elements,\n// even those that we disallow by default.\nexport const mathMlDisallowed = freeze([\n  'maction',\n  'maligngroup',\n  'malignmark',\n  'mlongdiv',\n  'mscarries',\n  'mscarry',\n  'msgroup',\n  'mstack',\n  'msline',\n  'msrow',\n  'semantics',\n  'annotation',\n  'annotation-xml',\n  'mprescripts',\n  'none',\n]);\n\nexport const text = freeze(['#text']);\n", "import { freeze } from './utils.js';\n\nexport const html = freeze([\n  'accept',\n  'action',\n  'align',\n  'alt',\n  'autocapitalize',\n  'autocomplete',\n  'autopictureinpicture',\n  'autoplay',\n  'background',\n  'bgcolor',\n  'border',\n  'capture',\n  'cellpadding',\n  'cellspacing',\n  'checked',\n  'cite',\n  'class',\n  'clear',\n  'color',\n  'cols',\n  'colspan',\n  'controls',\n  'controlslist',\n  'coords',\n  'crossorigin',\n  'datetime',\n  'decoding',\n  'default',\n  'dir',\n  'disabled',\n  'disablepictureinpicture',\n  'disableremoteplayback',\n  'download',\n  'draggable',\n  'enctype',\n  'enterkeyhint',\n  'face',\n  'for',\n  'headers',\n  'height',\n  'hidden',\n  'high',\n  'href',\n  'hreflang',\n  'id',\n  'inputmode',\n  'integrity',\n  'ismap',\n  'kind',\n  'label',\n  'lang',\n  'list',\n  'loading',\n  'loop',\n  'low',\n  'max',\n  'maxlength',\n  'media',\n  'method',\n  'min',\n  'minlength',\n  'multiple',\n  'muted',\n  'name',\n  'nonce',\n  'noshade',\n  'novalidate',\n  'nowrap',\n  'open',\n  'optimum',\n  'pattern',\n  'placeholder',\n  'playsinline',\n  'poster',\n  'preload',\n  'pubdate',\n  'radiogroup',\n  'readonly',\n  'rel',\n  'required',\n  'rev',\n  'reversed',\n  'role',\n  'rows',\n  'rowspan',\n  'spellcheck',\n  'scope',\n  'selected',\n  'shape',\n  'size',\n  'sizes',\n  'span',\n  'srclang',\n  'start',\n  'src',\n  'srcset',\n  'step',\n  'style',\n  'summary',\n  'tabindex',\n  'title',\n  'translate',\n  'type',\n  'usemap',\n  'valign',\n  'value',\n  'width',\n  'xmlns',\n  'slot',\n]);\n\nexport const svg = freeze([\n  'accent-height',\n  'accumulate',\n  'additive',\n  'alignment-baseline',\n  'ascent',\n  'attributename',\n  'attributetype',\n  'azimuth',\n  'basefrequency',\n  'baseline-shift',\n  'begin',\n  'bias',\n  'by',\n  'class',\n  'clip',\n  'clippathunits',\n  'clip-path',\n  'clip-rule',\n  'color',\n  'color-interpolation',\n  'color-interpolation-filters',\n  'color-profile',\n  'color-rendering',\n  'cx',\n  'cy',\n  'd',\n  'dx',\n  'dy',\n  'diffuseconstant',\n  'direction',\n  'display',\n  'divisor',\n  'dur',\n  'edgemode',\n  'elevation',\n  'end',\n  'fill',\n  'fill-opacity',\n  'fill-rule',\n  'filter',\n  'filterunits',\n  'flood-color',\n  'flood-opacity',\n  'font-family',\n  'font-size',\n  'font-size-adjust',\n  'font-stretch',\n  'font-style',\n  'font-variant',\n  'font-weight',\n  'fx',\n  'fy',\n  'g1',\n  'g2',\n  'glyph-name',\n  'glyphref',\n  'gradientunits',\n  'gradienttransform',\n  'height',\n  'href',\n  'id',\n  'image-rendering',\n  'in',\n  'in2',\n  'k',\n  'k1',\n  'k2',\n  'k3',\n  'k4',\n  'kerning',\n  'keypoints',\n  'keysplines',\n  'keytimes',\n  'lang',\n  'lengthadjust',\n  'letter-spacing',\n  'kernelmatrix',\n  'kernelunitlength',\n  'lighting-color',\n  'local',\n  'marker-end',\n  'marker-mid',\n  'marker-start',\n  'markerheight',\n  'markerunits',\n  'markerwidth',\n  'maskcontentunits',\n  'maskunits',\n  'max',\n  'mask',\n  'media',\n  'method',\n  'mode',\n  'min',\n  'name',\n  'numoctaves',\n  'offset',\n  'operator',\n  'opacity',\n  'order',\n  'orient',\n  'orientation',\n  'origin',\n  'overflow',\n  'paint-order',\n  'path',\n  'pathlength',\n  'patterncontentunits',\n  'patterntransform',\n  'patternunits',\n  'points',\n  'preservealpha',\n  'preserveaspectratio',\n  'primitiveunits',\n  'r',\n  'rx',\n  'ry',\n  'radius',\n  'refx',\n  'refy',\n  'repeatcount',\n  'repeatdur',\n  'restart',\n  'result',\n  'rotate',\n  'scale',\n  'seed',\n  'shape-rendering',\n  'specularconstant',\n  'specularexponent',\n  'spreadmethod',\n  'startoffset',\n  'stddeviation',\n  'stitchtiles',\n  'stop-color',\n  'stop-opacity',\n  'stroke-dasharray',\n  'stroke-dashoffset',\n  'stroke-linecap',\n  'stroke-linejoin',\n  'stroke-miterlimit',\n  'stroke-opacity',\n  'stroke',\n  'stroke-width',\n  'style',\n  'surfacescale',\n  'systemlanguage',\n  'tabindex',\n  'targetx',\n  'targety',\n  'transform',\n  'transform-origin',\n  'text-anchor',\n  'text-decoration',\n  'text-rendering',\n  'textlength',\n  'type',\n  'u1',\n  'u2',\n  'unicode',\n  'values',\n  'viewbox',\n  'visibility',\n  'version',\n  'vert-adv-y',\n  'vert-origin-x',\n  'vert-origin-y',\n  'width',\n  'word-spacing',\n  'wrap',\n  'writing-mode',\n  'xchannelselector',\n  'ychannelselector',\n  'x',\n  'x1',\n  'x2',\n  'xmlns',\n  'y',\n  'y1',\n  'y2',\n  'z',\n  'zoomandpan',\n]);\n\nexport const mathMl = freeze([\n  'accent',\n  'accentunder',\n  'align',\n  'bevelled',\n  'close',\n  'columnsalign',\n  'columnlines',\n  'columnspan',\n  'denomalign',\n  'depth',\n  'dir',\n  'display',\n  'displaystyle',\n  'encoding',\n  'fence',\n  'frame',\n  'height',\n  'href',\n  'id',\n  'largeop',\n  'length',\n  'linethickness',\n  'lspace',\n  'lquote',\n  'mathbackground',\n  'mathcolor',\n  'mathsize',\n  'mathvariant',\n  'maxsize',\n  'minsize',\n  'movablelimits',\n  'notation',\n  'numalign',\n  'open',\n  'rowalign',\n  'rowlines',\n  'rowspacing',\n  'rowspan',\n  'rspace',\n  'rquote',\n  'scriptlevel',\n  'scriptminsize',\n  'scriptsizemultiplier',\n  'selection',\n  'separator',\n  'separators',\n  'stretchy',\n  'subscriptshift',\n  'supscriptshift',\n  'symmetric',\n  'voffset',\n  'width',\n  'xmlns',\n]);\n\nexport const xml = freeze([\n  'xlink:href',\n  'xml:id',\n  'xlink:title',\n  'xml:space',\n  'xmlns:xlink',\n]);\n", "import { seal } from './utils.js';\n\n// eslint-disable-next-line unicorn/better-regex\nexport const MUSTACHE_EXPR = seal(/\\{\\{[\\w\\W]*|[\\w\\W]*\\}\\}/gm); // Specify template detection regex for SAFE_FOR_TEMPLATES mode\nexport const ERB_EXPR = seal(/<%[\\w\\W]*|[\\w\\W]*%>/gm);\nexport const TMPLIT_EXPR = seal(/\\${[\\w\\W]*}/gm);\nexport const DATA_ATTR = seal(/^data-[\\-\\w.\\u00B7-\\uFFFF]+$/); // eslint-disable-line no-useless-escape\nexport const ARIA_ATTR = seal(/^aria-[\\-\\w]+$/); // eslint-disable-line no-useless-escape\nexport const IS_ALLOWED_URI = seal(\n  /^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp):|[^a-z]|[a-z+.\\-]+(?:[^a-z+.\\-:]|$))/i // eslint-disable-line no-useless-escape\n);\nexport const IS_SCRIPT_OR_DATA = seal(/^(?:\\w+script|data):/i);\nexport const ATTR_WHITESPACE = seal(\n  /[\\u0000-\\u0020\\u00A0\\u1680\\u180E\\u2000-\\u2029\\u205F\\u3000]/g // eslint-disable-line no-control-regex\n);\nexport const DOCTYPE_NAME = seal(/^html$/i);\nexport const CUSTOM_ELEMENT = seal(/^[a-z][.\\w]*(-[.\\w]+)+$/i);\n", "import * as TAGS from './tags.js';\nimport * as ATTRS from './attrs.js';\nimport * as EXPRESSIONS from './regexp.js';\nimport {\n  addToSet,\n  clone,\n  freeze,\n  arrayForEach,\n  arrayPop,\n  arrayPush,\n  stringMatch,\n  stringReplace,\n  stringToLowerCase,\n  stringToString,\n  stringIndexOf,\n  stringTrim,\n  regExpTest,\n  typeErrorCreate,\n  lookupGetter,\n} from './utils.js';\n\nconst getGlobal = () => (typeof window === 'undefined' ? null : window);\n\n/**\n * Creates a no-op policy for internal use only.\n * Don't export this function outside this module!\n * @param {?TrustedTypePolicyFactory} trustedTypes The policy factory.\n * @param {Document} document The document object (to determine policy name suffix)\n * @return {?TrustedTypePolicy} The policy created (or null, if Trusted Types\n * are not supported).\n */\nconst _createTrustedTypesPolicy = function (trustedTypes, document) {\n  if (\n    typeof trustedTypes !== 'object' ||\n    typeof trustedTypes.createPolicy !== 'function'\n  ) {\n    return null;\n  }\n\n  // Allow the callers to control the unique policy name\n  // by adding a data-tt-policy-suffix to the script element with the DOMPurify.\n  // Policy creation with duplicate names throws in Trusted Types.\n  let suffix = null;\n  const ATTR_NAME = 'data-tt-policy-suffix';\n  if (\n    document.currentScript &&\n    document.currentScript.hasAttribute(ATTR_NAME)\n  ) {\n    suffix = document.currentScript.getAttribute(ATTR_NAME);\n  }\n\n  const policyName = 'dompurify' + (suffix ? '#' + suffix : '');\n\n  try {\n    return trustedTypes.createPolicy(policyName, {\n      createHTML(html) {\n        return html;\n      },\n      createScriptURL(scriptUrl) {\n        return scriptUrl;\n      },\n    });\n  } catch (_) {\n    // Policy creation failed (most likely another DOMPurify script has\n    // already run). Skip creating the policy, as this will only cause errors\n    // if TT are enforced.\n    console.warn(\n      'TrustedTypes policy ' + policyName + ' could not be created.'\n    );\n    return null;\n  }\n};\n\nfunction createDOMPurify(window = getGlobal()) {\n  const DOMPurify = (root) => createDOMPurify(root);\n\n  /**\n   * Version label, exposed for easier checks\n   * if DOMPurify is up to date or not\n   */\n  DOMPurify.version = VERSION;\n\n  /**\n   * Array of elements that DOMPurify removed during sanitation.\n   * Empty if nothing was removed.\n   */\n  DOMPurify.removed = [];\n\n  if (!window || !window.document || window.document.nodeType !== 9) {\n    // Not running in a browser, provide a factory function\n    // so that you can pass your own Window\n    DOMPurify.isSupported = false;\n\n    return DOMPurify;\n  }\n\n  const originalDocument = window.document;\n\n  let { document } = window;\n  const {\n    DocumentFragment,\n    HTMLTemplateElement,\n    Node,\n    Element,\n    NodeFilter,\n    NamedNodeMap = window.NamedNodeMap || window.MozNamedAttrMap,\n    HTMLFormElement,\n    DOMParser,\n    trustedTypes,\n  } = window;\n\n  const ElementPrototype = Element.prototype;\n\n  const cloneNode = lookupGetter(ElementPrototype, 'cloneNode');\n  const getNextSibling = lookupGetter(ElementPrototype, 'nextSibling');\n  const getChildNodes = lookupGetter(ElementPrototype, 'childNodes');\n  const getParentNode = lookupGetter(ElementPrototype, 'parentNode');\n\n  // As per issue #47, the web-components registry is inherited by a\n  // new document created via createHTMLDocument. As per the spec\n  // (http://w3c.github.io/webcomponents/spec/custom/#creating-and-passing-registries)\n  // a new empty registry is used when creating a template contents owner\n  // document, so we use that as our parent document to ensure nothing\n  // is inherited.\n  if (typeof HTMLTemplateElement === 'function') {\n    const template = document.createElement('template');\n    if (template.content && template.content.ownerDocument) {\n      document = template.content.ownerDocument;\n    }\n  }\n\n  const trustedTypesPolicy = _createTrustedTypesPolicy(\n    trustedTypes,\n    originalDocument\n  );\n  const emptyHTML = trustedTypesPolicy ? trustedTypesPolicy.createHTML('') : '';\n\n  const {\n    implementation,\n    createNodeIterator,\n    createDocumentFragment,\n    getElementsByTagName,\n  } = document;\n  const { importNode } = originalDocument;\n\n  let documentMode = {};\n  try {\n    documentMode = clone(document).documentMode ? document.documentMode : {};\n  } catch (_) {}\n\n  let hooks = {};\n\n  /**\n   * Expose whether this browser supports running the full DOMPurify.\n   */\n  DOMPurify.isSupported =\n    typeof getParentNode === 'function' &&\n    implementation &&\n    implementation.createHTMLDocument !== undefined &&\n    documentMode !== 9;\n\n  const {\n    MUSTACHE_EXPR,\n    ERB_EXPR,\n    TMPLIT_EXPR,\n    DATA_ATTR,\n    ARIA_ATTR,\n    IS_SCRIPT_OR_DATA,\n    ATTR_WHITESPACE,\n    CUSTOM_ELEMENT,\n  } = EXPRESSIONS;\n\n  let { IS_ALLOWED_URI } = EXPRESSIONS;\n\n  /**\n   * We consider the elements and attributes below to be safe. Ideally\n   * don't add any new ones but feel free to remove unwanted ones.\n   */\n\n  /* allowed element names */\n  let ALLOWED_TAGS = null;\n  const DEFAULT_ALLOWED_TAGS = addToSet({}, [\n    ...TAGS.html,\n    ...TAGS.svg,\n    ...TAGS.svgFilters,\n    ...TAGS.mathMl,\n    ...TAGS.text,\n  ]);\n\n  /* Allowed attribute names */\n  let ALLOWED_ATTR = null;\n  const DEFAULT_ALLOWED_ATTR = addToSet({}, [\n    ...ATTRS.html,\n    ...ATTRS.svg,\n    ...ATTRS.mathMl,\n    ...ATTRS.xml,\n  ]);\n\n  /*\n   * Configure how DOMPUrify should handle custom elements and their attributes as well as customized built-in elements.\n   * @property {RegExp|Function|null} tagNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any custom elements)\n   * @property {RegExp|Function|null} attributeNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any attributes not on the allow list)\n   * @property {boolean} allowCustomizedBuiltInElements allow custom elements derived from built-ins if they pass CUSTOM_ELEMENT_HANDLING.tagNameCheck. Default: `false`.\n   */\n  let CUSTOM_ELEMENT_HANDLING = Object.seal(\n    Object.create(null, {\n      tagNameCheck: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: null,\n      },\n      attributeNameCheck: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: null,\n      },\n      allowCustomizedBuiltInElements: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: false,\n      },\n    })\n  );\n\n  /* Explicitly forbidden tags (overrides ALLOWED_TAGS/ADD_TAGS) */\n  let FORBID_TAGS = null;\n\n  /* Explicitly forbidden attributes (overrides ALLOWED_ATTR/ADD_ATTR) */\n  let FORBID_ATTR = null;\n\n  /* Decide if ARIA attributes are okay */\n  let ALLOW_ARIA_ATTR = true;\n\n  /* Decide if custom data attributes are okay */\n  let ALLOW_DATA_ATTR = true;\n\n  /* Decide if unknown protocols are okay */\n  let ALLOW_UNKNOWN_PROTOCOLS = false;\n\n  /* Decide if self-closing tags in attributes are allowed.\n   * Usually removed due to a mXSS issue in jQuery 3.0 */\n  let ALLOW_SELF_CLOSE_IN_ATTR = true;\n\n  /* Output should be safe for common template engines.\n   * This means, DOMPurify removes data attributes, mustaches and ERB\n   */\n  let SAFE_FOR_TEMPLATES = false;\n\n  /* Output should be safe even for XML used within HTML and alike.\n   * This means, DOMPurify removes comments when containing risky content.\n   */\n  let SAFE_FOR_XML = true;\n\n  /* Decide if document with <html>... should be returned */\n  let WHOLE_DOCUMENT = false;\n\n  /* Track whether config is already set on this instance of DOMPurify. */\n  let SET_CONFIG = false;\n\n  /* Decide if all elements (e.g. style, script) must be children of\n   * document.body. By default, browsers might move them to document.head */\n  let FORCE_BODY = false;\n\n  /* Decide if a DOM `HTMLBodyElement` should be returned, instead of a html\n   * string (or a TrustedHTML object if Trusted Types are supported).\n   * If `WHOLE_DOCUMENT` is enabled a `HTMLHtmlElement` will be returned instead\n   */\n  let RETURN_DOM = false;\n\n  /* Decide if a DOM `DocumentFragment` should be returned, instead of a html\n   * string  (or a TrustedHTML object if Trusted Types are supported) */\n  let RETURN_DOM_FRAGMENT = false;\n\n  /* Try to return a Trusted Type object instead of a string, return a string in\n   * case Trusted Types are not supported  */\n  let RETURN_TRUSTED_TYPE = false;\n\n  /* Output should be free from DOM clobbering attacks?\n   * This sanitizes markups named with colliding, clobberable built-in DOM APIs.\n   */\n  let SANITIZE_DOM = true;\n\n  /* Achieve full DOM Clobbering protection by isolating the namespace of named\n   * properties and JS variables, mitigating attacks that abuse the HTML/DOM spec rules.\n   *\n   * HTML/DOM spec rules that enable DOM Clobbering:\n   *   - Named Access on Window (§7.3.3)\n   *   - DOM Tree Accessors (§3.1.5)\n   *   - Form Element Parent-Child Relations (§4.10.3)\n   *   - Iframe srcdoc / Nested WindowProxies (§4.8.5)\n   *   - HTMLCollection (§4.2.10.2)\n   *\n   * Namespace isolation is implemented by prefixing `id` and `name` attributes\n   * with a constant string, i.e., `user-content-`\n   */\n  let SANITIZE_NAMED_PROPS = false;\n  const SANITIZE_NAMED_PROPS_PREFIX = 'user-content-';\n\n  /* Keep element content when removing element? */\n  let KEEP_CONTENT = true;\n\n  /* If a `Node` is passed to sanitize(), then performs sanitization in-place instead\n   * of importing it into a new Document and returning a sanitized copy */\n  let IN_PLACE = false;\n\n  /* Allow usage of profiles like html, svg and mathMl */\n  let USE_PROFILES = {};\n\n  /* Tags to ignore content of when KEEP_CONTENT is true */\n  let FORBID_CONTENTS = null;\n  const DEFAULT_FORBID_CONTENTS = addToSet({}, [\n    'annotation-xml',\n    'audio',\n    'colgroup',\n    'desc',\n    'foreignobject',\n    'head',\n    'iframe',\n    'math',\n    'mi',\n    'mn',\n    'mo',\n    'ms',\n    'mtext',\n    'noembed',\n    'noframes',\n    'noscript',\n    'plaintext',\n    'script',\n    'style',\n    'svg',\n    'template',\n    'thead',\n    'title',\n    'video',\n    'xmp',\n  ]);\n\n  /* Tags that are safe for data: URIs */\n  let DATA_URI_TAGS = null;\n  const DEFAULT_DATA_URI_TAGS = addToSet({}, [\n    'audio',\n    'video',\n    'img',\n    'source',\n    'image',\n    'track',\n  ]);\n\n  /* Attributes safe for values like \"javascript:\" */\n  let URI_SAFE_ATTRIBUTES = null;\n  const DEFAULT_URI_SAFE_ATTRIBUTES = addToSet({}, [\n    'alt',\n    'class',\n    'for',\n    'id',\n    'label',\n    'name',\n    'pattern',\n    'placeholder',\n    'role',\n    'summary',\n    'title',\n    'value',\n    'style',\n    'xmlns',\n  ]);\n\n  const MATHML_NAMESPACE = 'http://www.w3.org/1998/Math/MathML';\n  const SVG_NAMESPACE = 'http://www.w3.org/2000/svg';\n  const HTML_NAMESPACE = 'http://www.w3.org/1999/xhtml';\n  /* Document namespace */\n  let NAMESPACE = HTML_NAMESPACE;\n  let IS_EMPTY_INPUT = false;\n\n  /* Allowed XHTML+XML namespaces */\n  let ALLOWED_NAMESPACES = null;\n  const DEFAULT_ALLOWED_NAMESPACES = addToSet(\n    {},\n    [MATHML_NAMESPACE, SVG_NAMESPACE, HTML_NAMESPACE],\n    stringToString\n  );\n\n  /* Parsing of strict XHTML documents */\n  let PARSER_MEDIA_TYPE;\n  const SUPPORTED_PARSER_MEDIA_TYPES = ['application/xhtml+xml', 'text/html'];\n  const DEFAULT_PARSER_MEDIA_TYPE = 'text/html';\n  let transformCaseFunc;\n\n  /* Keep a reference to config to pass to hooks */\n  let CONFIG = null;\n\n  /* Ideally, do not touch anything below this line */\n  /* ______________________________________________ */\n\n  const formElement = document.createElement('form');\n\n  const isRegexOrFunction = function (testValue) {\n    return testValue instanceof RegExp || testValue instanceof Function;\n  };\n\n  /**\n   * _parseConfig\n   *\n   * @param  {Object} cfg optional config literal\n   */\n  // eslint-disable-next-line complexity\n  const _parseConfig = function (cfg) {\n    if (CONFIG && CONFIG === cfg) {\n      return;\n    }\n\n    /* Shield configuration object from tampering */\n    if (!cfg || typeof cfg !== 'object') {\n      cfg = {};\n    }\n\n    /* Shield configuration object from prototype pollution */\n    cfg = clone(cfg);\n\n    PARSER_MEDIA_TYPE =\n      // eslint-disable-next-line unicorn/prefer-includes\n      SUPPORTED_PARSER_MEDIA_TYPES.indexOf(cfg.PARSER_MEDIA_TYPE) === -1\n        ? (PARSER_MEDIA_TYPE = DEFAULT_PARSER_MEDIA_TYPE)\n        : (PARSER_MEDIA_TYPE = cfg.PARSER_MEDIA_TYPE);\n\n    // HTML tags and attributes are not case-sensitive, converting to lowercase. Keeping XHTML as is.\n    transformCaseFunc =\n      PARSER_MEDIA_TYPE === 'application/xhtml+xml'\n        ? stringToString\n        : stringToLowerCase;\n\n    /* Set configuration parameters */\n    ALLOWED_TAGS =\n      'ALLOWED_TAGS' in cfg\n        ? addToSet({}, cfg.ALLOWED_TAGS, transformCaseFunc)\n        : DEFAULT_ALLOWED_TAGS;\n    ALLOWED_ATTR =\n      'ALLOWED_ATTR' in cfg\n        ? addToSet({}, cfg.ALLOWED_ATTR, transformCaseFunc)\n        : DEFAULT_ALLOWED_ATTR;\n    ALLOWED_NAMESPACES =\n      'ALLOWED_NAMESPACES' in cfg\n        ? addToSet({}, cfg.ALLOWED_NAMESPACES, stringToString)\n        : DEFAULT_ALLOWED_NAMESPACES;\n    URI_SAFE_ATTRIBUTES =\n      'ADD_URI_SAFE_ATTR' in cfg\n        ? addToSet(\n            clone(DEFAULT_URI_SAFE_ATTRIBUTES), // eslint-disable-line indent\n            cfg.ADD_URI_SAFE_ATTR, // eslint-disable-line indent\n            transformCaseFunc // eslint-disable-line indent\n          ) // eslint-disable-line indent\n        : DEFAULT_URI_SAFE_ATTRIBUTES;\n    DATA_URI_TAGS =\n      'ADD_DATA_URI_TAGS' in cfg\n        ? addToSet(\n            clone(DEFAULT_DATA_URI_TAGS), // eslint-disable-line indent\n            cfg.ADD_DATA_URI_TAGS, // eslint-disable-line indent\n            transformCaseFunc // eslint-disable-line indent\n          ) // eslint-disable-line indent\n        : DEFAULT_DATA_URI_TAGS;\n    FORBID_CONTENTS =\n      'FORBID_CONTENTS' in cfg\n        ? addToSet({}, cfg.FORBID_CONTENTS, transformCaseFunc)\n        : DEFAULT_FORBID_CONTENTS;\n    FORBID_TAGS =\n      'FORBID_TAGS' in cfg\n        ? addToSet({}, cfg.FORBID_TAGS, transformCaseFunc)\n        : {};\n    FORBID_ATTR =\n      'FORBID_ATTR' in cfg\n        ? addToSet({}, cfg.FORBID_ATTR, transformCaseFunc)\n        : {};\n    USE_PROFILES = 'USE_PROFILES' in cfg ? cfg.USE_PROFILES : false;\n    ALLOW_ARIA_ATTR = cfg.ALLOW_ARIA_ATTR !== false; // Default true\n    ALLOW_DATA_ATTR = cfg.ALLOW_DATA_ATTR !== false; // Default true\n    ALLOW_UNKNOWN_PROTOCOLS = cfg.ALLOW_UNKNOWN_PROTOCOLS || false; // Default false\n    ALLOW_SELF_CLOSE_IN_ATTR = cfg.ALLOW_SELF_CLOSE_IN_ATTR !== false; // Default true\n    SAFE_FOR_TEMPLATES = cfg.SAFE_FOR_TEMPLATES || false; // Default false\n    SAFE_FOR_XML = cfg.SAFE_FOR_XML !== false; // Default true\n    WHOLE_DOCUMENT = cfg.WHOLE_DOCUMENT || false; // Default false\n    RETURN_DOM = cfg.RETURN_DOM || false; // Default false\n    RETURN_DOM_FRAGMENT = cfg.RETURN_DOM_FRAGMENT || false; // Default false\n    RETURN_TRUSTED_TYPE = cfg.RETURN_TRUSTED_TYPE || false; // Default false\n    FORCE_BODY = cfg.FORCE_BODY || false; // Default false\n    SANITIZE_DOM = cfg.SANITIZE_DOM !== false; // Default true\n    SANITIZE_NAMED_PROPS = cfg.SANITIZE_NAMED_PROPS || false; // Default false\n    KEEP_CONTENT = cfg.KEEP_CONTENT !== false; // Default true\n    IN_PLACE = cfg.IN_PLACE || false; // Default false\n    IS_ALLOWED_URI = cfg.ALLOWED_URI_REGEXP || IS_ALLOWED_URI;\n    NAMESPACE = cfg.NAMESPACE || HTML_NAMESPACE;\n    CUSTOM_ELEMENT_HANDLING = cfg.CUSTOM_ELEMENT_HANDLING || {};\n    if (\n      cfg.CUSTOM_ELEMENT_HANDLING &&\n      isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck)\n    ) {\n      CUSTOM_ELEMENT_HANDLING.tagNameCheck =\n        cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck;\n    }\n\n    if (\n      cfg.CUSTOM_ELEMENT_HANDLING &&\n      isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)\n    ) {\n      CUSTOM_ELEMENT_HANDLING.attributeNameCheck =\n        cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck;\n    }\n\n    if (\n      cfg.CUSTOM_ELEMENT_HANDLING &&\n      typeof cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements ===\n        'boolean'\n    ) {\n      CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements =\n        cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements;\n    }\n\n    if (SAFE_FOR_TEMPLATES) {\n      ALLOW_DATA_ATTR = false;\n    }\n\n    if (RETURN_DOM_FRAGMENT) {\n      RETURN_DOM = true;\n    }\n\n    /* Parse profile info */\n    if (USE_PROFILES) {\n      ALLOWED_TAGS = addToSet({}, [...TAGS.text]);\n      ALLOWED_ATTR = [];\n      if (USE_PROFILES.html === true) {\n        addToSet(ALLOWED_TAGS, TAGS.html);\n        addToSet(ALLOWED_ATTR, ATTRS.html);\n      }\n\n      if (USE_PROFILES.svg === true) {\n        addToSet(ALLOWED_TAGS, TAGS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n\n      if (USE_PROFILES.svgFilters === true) {\n        addToSet(ALLOWED_TAGS, TAGS.svgFilters);\n        addToSet(ALLOWED_ATTR, ATTRS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n\n      if (USE_PROFILES.mathMl === true) {\n        addToSet(ALLOWED_TAGS, TAGS.mathMl);\n        addToSet(ALLOWED_ATTR, ATTRS.mathMl);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n    }\n\n    /* Merge configuration parameters */\n    if (cfg.ADD_TAGS) {\n      if (ALLOWED_TAGS === DEFAULT_ALLOWED_TAGS) {\n        ALLOWED_TAGS = clone(ALLOWED_TAGS);\n      }\n\n      addToSet(ALLOWED_TAGS, cfg.ADD_TAGS, transformCaseFunc);\n    }\n\n    if (cfg.ADD_ATTR) {\n      if (ALLOWED_ATTR === DEFAULT_ALLOWED_ATTR) {\n        ALLOWED_ATTR = clone(ALLOWED_ATTR);\n      }\n\n      addToSet(ALLOWED_ATTR, cfg.ADD_ATTR, transformCaseFunc);\n    }\n\n    if (cfg.ADD_URI_SAFE_ATTR) {\n      addToSet(URI_SAFE_ATTRIBUTES, cfg.ADD_URI_SAFE_ATTR, transformCaseFunc);\n    }\n\n    if (cfg.FORBID_CONTENTS) {\n      if (FORBID_CONTENTS === DEFAULT_FORBID_CONTENTS) {\n        FORBID_CONTENTS = clone(FORBID_CONTENTS);\n      }\n\n      addToSet(FORBID_CONTENTS, cfg.FORBID_CONTENTS, transformCaseFunc);\n    }\n\n    /* Add #text in case KEEP_CONTENT is set to true */\n    if (KEEP_CONTENT) {\n      ALLOWED_TAGS['#text'] = true;\n    }\n\n    /* Add html, head and body to ALLOWED_TAGS in case WHOLE_DOCUMENT is true */\n    if (WHOLE_DOCUMENT) {\n      addToSet(ALLOWED_TAGS, ['html', 'head', 'body']);\n    }\n\n    /* Add tbody to ALLOWED_TAGS in case tables are permitted, see #286, #365 */\n    if (ALLOWED_TAGS.table) {\n      addToSet(ALLOWED_TAGS, ['tbody']);\n      delete FORBID_TAGS.tbody;\n    }\n\n    // Prevent further manipulation of configuration.\n    // Not available in IE8, Safari 5, etc.\n    if (freeze) {\n      freeze(cfg);\n    }\n\n    CONFIG = cfg;\n  };\n\n  const MATHML_TEXT_INTEGRATION_POINTS = addToSet({}, [\n    'mi',\n    'mo',\n    'mn',\n    'ms',\n    'mtext',\n  ]);\n\n  const HTML_INTEGRATION_POINTS = addToSet({}, ['annotation-xml']);\n\n  // Certain elements are allowed in both SVG and HTML\n  // namespace. We need to specify them explicitly\n  // so that they don't get erroneously deleted from\n  // HTML namespace.\n  const COMMON_SVG_AND_HTML_ELEMENTS = addToSet({}, [\n    'title',\n    'style',\n    'font',\n    'a',\n    'script',\n  ]);\n\n  /* Keep track of all possible SVG and MathML tags\n   * so that we can perform the namespace checks\n   * correctly. */\n  const ALL_SVG_TAGS = addToSet({}, TAGS.svg);\n  addToSet(ALL_SVG_TAGS, TAGS.svgFilters);\n  addToSet(ALL_SVG_TAGS, TAGS.svgDisallowed);\n\n  const ALL_MATHML_TAGS = addToSet({}, TAGS.mathMl);\n  addToSet(ALL_MATHML_TAGS, TAGS.mathMlDisallowed);\n\n  /**\n   *\n   *\n   * @param  {Element} element a DOM element whose namespace is being checked\n   * @returns {boolean} Return false if the element has a\n   *  namespace that a spec-compliant parser would never\n   *  return. Return true otherwise.\n   */\n  const _checkValidNamespace = function (element) {\n    let parent = getParentNode(element);\n\n    // In JSDOM, if we're inside shadow DOM, then parentNode\n    // can be null. We just simulate parent in this case.\n    if (!parent || !parent.tagName) {\n      parent = {\n        namespaceURI: NAMESPACE,\n        tagName: 'template',\n      };\n    }\n\n    const tagName = stringToLowerCase(element.tagName);\n    const parentTagName = stringToLowerCase(parent.tagName);\n\n    if (!ALLOWED_NAMESPACES[element.namespaceURI]) {\n      return false;\n    }\n\n    if (element.namespaceURI === SVG_NAMESPACE) {\n      // The only way to switch from HTML namespace to SVG\n      // is via <svg>. If it happens via any other tag, then\n      // it should be killed.\n      if (parent.namespaceURI === HTML_NAMESPACE) {\n        return tagName === 'svg';\n      }\n\n      // The only way to switch from MathML to SVG is via`\n      // svg if parent is either <annotation-xml> or MathML\n      // text integration points.\n      if (parent.namespaceURI === MATHML_NAMESPACE) {\n        return (\n          tagName === 'svg' &&\n          (parentTagName === 'annotation-xml' ||\n            MATHML_TEXT_INTEGRATION_POINTS[parentTagName])\n        );\n      }\n\n      // We only allow elements that are defined in SVG\n      // spec. All others are disallowed in SVG namespace.\n      return Boolean(ALL_SVG_TAGS[tagName]);\n    }\n\n    if (element.namespaceURI === MATHML_NAMESPACE) {\n      // The only way to switch from HTML namespace to MathML\n      // is via <math>. If it happens via any other tag, then\n      // it should be killed.\n      if (parent.namespaceURI === HTML_NAMESPACE) {\n        return tagName === 'math';\n      }\n\n      // The only way to switch from SVG to MathML is via\n      // <math> and HTML integration points\n      if (parent.namespaceURI === SVG_NAMESPACE) {\n        return tagName === 'math' && HTML_INTEGRATION_POINTS[parentTagName];\n      }\n\n      // We only allow elements that are defined in MathML\n      // spec. All others are disallowed in MathML namespace.\n      return Boolean(ALL_MATHML_TAGS[tagName]);\n    }\n\n    if (element.namespaceURI === HTML_NAMESPACE) {\n      // The only way to switch from SVG to HTML is via\n      // HTML integration points, and from MathML to HTML\n      // is via MathML text integration points\n      if (\n        parent.namespaceURI === SVG_NAMESPACE &&\n        !HTML_INTEGRATION_POINTS[parentTagName]\n      ) {\n        return false;\n      }\n\n      if (\n        parent.namespaceURI === MATHML_NAMESPACE &&\n        !MATHML_TEXT_INTEGRATION_POINTS[parentTagName]\n      ) {\n        return false;\n      }\n\n      // We disallow tags that are specific for MathML\n      // or SVG and should never appear in HTML namespace\n      return (\n        !ALL_MATHML_TAGS[tagName] &&\n        (COMMON_SVG_AND_HTML_ELEMENTS[tagName] || !ALL_SVG_TAGS[tagName])\n      );\n    }\n\n    // For XHTML and XML documents that support custom namespaces\n    if (\n      PARSER_MEDIA_TYPE === 'application/xhtml+xml' &&\n      ALLOWED_NAMESPACES[element.namespaceURI]\n    ) {\n      return true;\n    }\n\n    // The code should never reach this place (this means\n    // that the element somehow got namespace that is not\n    // HTML, SVG, MathML or allowed via ALLOWED_NAMESPACES).\n    // Return false just in case.\n    return false;\n  };\n\n  /**\n   * _forceRemove\n   *\n   * @param  {Node} node a DOM node\n   */\n  const _forceRemove = function (node) {\n    arrayPush(DOMPurify.removed, { element: node });\n    try {\n      // eslint-disable-next-line unicorn/prefer-dom-node-remove\n      node.parentNode.removeChild(node);\n    } catch (_) {\n      try {\n        node.outerHTML = emptyHTML;\n      } catch (_) {\n        node.remove();\n      }\n    }\n  };\n\n  /**\n   * _removeAttribute\n   *\n   * @param  {String} name an Attribute name\n   * @param  {Node} node a DOM node\n   */\n  const _removeAttribute = function (name, node) {\n    try {\n      arrayPush(DOMPurify.removed, {\n        attribute: node.getAttributeNode(name),\n        from: node,\n      });\n    } catch (_) {\n      arrayPush(DOMPurify.removed, {\n        attribute: null,\n        from: node,\n      });\n    }\n\n    node.removeAttribute(name);\n\n    // We void attribute values for unremovable \"is\"\" attributes\n    if (name === 'is' && !ALLOWED_ATTR[name]) {\n      if (RETURN_DOM || RETURN_DOM_FRAGMENT) {\n        try {\n          _forceRemove(node);\n        } catch (_) {}\n      } else {\n        try {\n          node.setAttribute(name, '');\n        } catch (_) {}\n      }\n    }\n  };\n\n  /**\n   * _initDocument\n   *\n   * @param  {String} dirty a string of dirty markup\n   * @return {Document} a DOM, filled with the dirty markup\n   */\n  const _initDocument = function (dirty) {\n    /* Create a HTML document */\n    let doc;\n    let leadingWhitespace;\n\n    if (FORCE_BODY) {\n      dirty = '<remove></remove>' + dirty;\n    } else {\n      /* If FORCE_BODY isn't used, leading whitespace needs to be preserved manually */\n      const matches = stringMatch(dirty, /^[\\r\\n\\t ]+/);\n      leadingWhitespace = matches && matches[0];\n    }\n\n    if (\n      PARSER_MEDIA_TYPE === 'application/xhtml+xml' &&\n      NAMESPACE === HTML_NAMESPACE\n    ) {\n      // Root of XHTML doc must contain xmlns declaration (see https://www.w3.org/TR/xhtml1/normative.html#strict)\n      dirty =\n        '<html xmlns=\"http://www.w3.org/1999/xhtml\"><head></head><body>' +\n        dirty +\n        '</body></html>';\n    }\n\n    const dirtyPayload = trustedTypesPolicy\n      ? trustedTypesPolicy.createHTML(dirty)\n      : dirty;\n    /*\n     * Use the DOMParser API by default, fallback later if needs be\n     * DOMParser not work for svg when has multiple root element.\n     */\n    if (NAMESPACE === HTML_NAMESPACE) {\n      try {\n        doc = new DOMParser().parseFromString(dirtyPayload, PARSER_MEDIA_TYPE);\n      } catch (_) {}\n    }\n\n    /* Use createHTMLDocument in case DOMParser is not available */\n    if (!doc || !doc.documentElement) {\n      doc = implementation.createDocument(NAMESPACE, 'template', null);\n      try {\n        doc.documentElement.innerHTML = IS_EMPTY_INPUT\n          ? emptyHTML\n          : dirtyPayload;\n      } catch (_) {\n        // Syntax error if dirtyPayload is invalid xml\n      }\n    }\n\n    const body = doc.body || doc.documentElement;\n\n    if (dirty && leadingWhitespace) {\n      body.insertBefore(\n        document.createTextNode(leadingWhitespace),\n        body.childNodes[0] || null\n      );\n    }\n\n    /* Work on whole document or just its body */\n    if (NAMESPACE === HTML_NAMESPACE) {\n      return getElementsByTagName.call(\n        doc,\n        WHOLE_DOCUMENT ? 'html' : 'body'\n      )[0];\n    }\n\n    return WHOLE_DOCUMENT ? doc.documentElement : body;\n  };\n\n  /**\n   * _createIterator\n   *\n   * @param  {Document} root document/fragment to create iterator for\n   * @return {Iterator} iterator instance\n   */\n  const _createIterator = function (root) {\n    return createNodeIterator.call(\n      root.ownerDocument || root,\n      root,\n      // eslint-disable-next-line no-bitwise\n      NodeFilter.SHOW_ELEMENT |\n        NodeFilter.SHOW_COMMENT |\n        NodeFilter.SHOW_TEXT |\n        NodeFilter.SHOW_PROCESSING_INSTRUCTION |\n        NodeFilter.SHOW_CDATA_SECTION,\n      null,\n      false\n    );\n  };\n\n  /**\n   * _isClobbered\n   *\n   * @param  {Node} elm element to check for clobbering attacks\n   * @return {Boolean} true if clobbered, false if safe\n   */\n  const _isClobbered = function (elm) {\n    return (\n      elm instanceof HTMLFormElement &&\n      (typeof elm.nodeName !== 'string' ||\n        typeof elm.textContent !== 'string' ||\n        typeof elm.removeChild !== 'function' ||\n        !(elm.attributes instanceof NamedNodeMap) ||\n        typeof elm.removeAttribute !== 'function' ||\n        typeof elm.setAttribute !== 'function' ||\n        typeof elm.namespaceURI !== 'string' ||\n        typeof elm.insertBefore !== 'function' ||\n        typeof elm.hasChildNodes !== 'function')\n    );\n  };\n\n  /**\n   * _isNode\n   *\n   * @param  {Node} obj object to check whether it's a DOM node\n   * @return {Boolean} true is object is a DOM node\n   */\n  const _isNode = function (object) {\n    return typeof Node === 'object'\n      ? object instanceof Node\n      : object &&\n          typeof object === 'object' &&\n          typeof object.nodeType === 'number' &&\n          typeof object.nodeName === 'string';\n  };\n\n  /**\n   * _executeHook\n   * Execute user configurable hooks\n   *\n   * @param  {String} entryPoint  Name of the hook's entry point\n   * @param  {Node} currentNode node to work on with the hook\n   * @param  {Object} data additional hook parameters\n   */\n  const _executeHook = function (entryPoint, currentNode, data) {\n    if (!hooks[entryPoint]) {\n      return;\n    }\n\n    arrayForEach(hooks[entryPoint], (hook) => {\n      hook.call(DOMPurify, currentNode, data, CONFIG);\n    });\n  };\n\n  /**\n   * _sanitizeElements\n   *\n   * @protect nodeName\n   * @protect textContent\n   * @protect removeChild\n   *\n   * @param   {Node} currentNode to check for permission to exist\n   * @return  {Boolean} true if node was killed, false if left alive\n   */\n  const _sanitizeElements = function (currentNode) {\n    let content;\n\n    /* Execute a hook if present */\n    _executeHook('beforeSanitizeElements', currentNode, null);\n\n    /* Check if element is clobbered or can clobber */\n    if (_isClobbered(currentNode)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Check if tagname contains Unicode */\n    if (regExpTest(/[\\u0080-\\uFFFF]/, currentNode.nodeName)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Now let's check the element's type and name */\n    const tagName = transformCaseFunc(currentNode.nodeName);\n\n    /* Execute a hook if present */\n    _executeHook('uponSanitizeElement', currentNode, {\n      tagName,\n      allowedTags: ALLOWED_TAGS,\n    });\n\n    /* Detect mXSS attempts abusing namespace confusion */\n    if (\n      currentNode.hasChildNodes() &&\n      !_isNode(currentNode.firstElementChild) &&\n      (!_isNode(currentNode.content) ||\n        !_isNode(currentNode.content.firstElementChild)) &&\n      regExpTest(/<[/\\w]/g, currentNode.innerHTML) &&\n      regExpTest(/<[/\\w]/g, currentNode.textContent)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Mitigate a problem with templates inside select */\n    if (\n      tagName === 'select' &&\n      regExpTest(/<template/i, currentNode.innerHTML)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Remove any ocurrence of processing instructions */\n    if (currentNode.nodeType === 7) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Remove any kind of possibly harmful comments */\n    if (\n      SAFE_FOR_XML &&\n      currentNode.nodeType === 8 &&\n      regExpTest(/<[/\\w]/g, currentNode.data)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Remove element if anything forbids its presence */\n    if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n      /* Check if we have a custom element to handle */\n      if (!FORBID_TAGS[tagName] && _basicCustomElementTest(tagName)) {\n        if (\n          CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp &&\n          regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, tagName)\n        )\n          return false;\n        if (\n          CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function &&\n          CUSTOM_ELEMENT_HANDLING.tagNameCheck(tagName)\n        )\n          return false;\n      }\n\n      /* Keep content except for bad-listed elements */\n      if (KEEP_CONTENT && !FORBID_CONTENTS[tagName]) {\n        const parentNode = getParentNode(currentNode) || currentNode.parentNode;\n        const childNodes = getChildNodes(currentNode) || currentNode.childNodes;\n\n        if (childNodes && parentNode) {\n          const childCount = childNodes.length;\n\n          for (let i = childCount - 1; i >= 0; --i) {\n            const childClone = cloneNode(childNodes[i], true);\n            childClone.__removalCount = (currentNode.__removalCount || 0) + 1;\n            parentNode.insertBefore(childClone, getNextSibling(currentNode));\n          }\n        }\n      }\n\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Check whether element has a valid namespace */\n    if (currentNode instanceof Element && !_checkValidNamespace(currentNode)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Make sure that older browsers don't get fallback-tag mXSS */\n    if (\n      (tagName === 'noscript' ||\n        tagName === 'noembed' ||\n        tagName === 'noframes') &&\n      regExpTest(/<\\/no(script|embed|frames)/i, currentNode.innerHTML)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Sanitize element content to be template-safe */\n    if (SAFE_FOR_TEMPLATES && currentNode.nodeType === 3) {\n      /* Get the element's text content */\n      content = currentNode.textContent;\n      content = stringReplace(content, MUSTACHE_EXPR, ' ');\n      content = stringReplace(content, ERB_EXPR, ' ');\n      content = stringReplace(content, TMPLIT_EXPR, ' ');\n      if (currentNode.textContent !== content) {\n        arrayPush(DOMPurify.removed, { element: currentNode.cloneNode() });\n        currentNode.textContent = content;\n      }\n    }\n\n    /* Execute a hook if present */\n    _executeHook('afterSanitizeElements', currentNode, null);\n\n    return false;\n  };\n\n  /**\n   * _isValidAttribute\n   *\n   * @param  {string} lcTag Lowercase tag name of containing element.\n   * @param  {string} lcName Lowercase attribute name.\n   * @param  {string} value Attribute value.\n   * @return {Boolean} Returns true if `value` is valid, otherwise false.\n   */\n  // eslint-disable-next-line complexity\n  const _isValidAttribute = function (lcTag, lcName, value) {\n    /* Make sure attribute cannot clobber */\n    if (\n      SANITIZE_DOM &&\n      (lcName === 'id' || lcName === 'name') &&\n      (value in document || value in formElement)\n    ) {\n      return false;\n    }\n\n    /* Allow valid data-* attributes: At least one character after \"-\"\n        (https://html.spec.whatwg.org/multipage/dom.html#embedding-custom-non-visible-data-with-the-data-*-attributes)\n        XML-compatible (https://html.spec.whatwg.org/multipage/infrastructure.html#xml-compatible and http://www.w3.org/TR/xml/#d0e804)\n        We don't need to check the value; it's always URI safe. */\n    if (\n      ALLOW_DATA_ATTR &&\n      !FORBID_ATTR[lcName] &&\n      regExpTest(DATA_ATTR, lcName)\n    ) {\n      // This attribute is safe\n    } else if (ALLOW_ARIA_ATTR && regExpTest(ARIA_ATTR, lcName)) {\n      // This attribute is safe\n      /* Otherwise, check the name is permitted */\n    } else if (!ALLOWED_ATTR[lcName] || FORBID_ATTR[lcName]) {\n      if (\n        // First condition does a very basic check if a) it's basically a valid custom element tagname AND\n        // b) if the tagName passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n        // and c) if the attribute name passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.attributeNameCheck\n        (_basicCustomElementTest(lcTag) &&\n          ((CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp &&\n            regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, lcTag)) ||\n            (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function &&\n              CUSTOM_ELEMENT_HANDLING.tagNameCheck(lcTag))) &&\n          ((CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof RegExp &&\n            regExpTest(CUSTOM_ELEMENT_HANDLING.attributeNameCheck, lcName)) ||\n            (CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof Function &&\n              CUSTOM_ELEMENT_HANDLING.attributeNameCheck(lcName)))) ||\n        // Alternative, second condition checks if it's an `is`-attribute, AND\n        // the value passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n        (lcName === 'is' &&\n          CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements &&\n          ((CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp &&\n            regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, value)) ||\n            (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function &&\n              CUSTOM_ELEMENT_HANDLING.tagNameCheck(value))))\n      ) {\n        // If user has supplied a regexp or function in CUSTOM_ELEMENT_HANDLING.tagNameCheck, we need to also allow derived custom elements using the same tagName test.\n        // Additionally, we need to allow attributes passing the CUSTOM_ELEMENT_HANDLING.attributeNameCheck user has configured, as custom elements can define these at their own discretion.\n      } else {\n        return false;\n      }\n      /* Check value is safe. First, is attr inert? If so, is safe */\n    } else if (URI_SAFE_ATTRIBUTES[lcName]) {\n      // This attribute is safe\n      /* Check no script, data or unknown possibly unsafe URI\n        unless we know URI values are safe for that attribute */\n    } else if (\n      regExpTest(IS_ALLOWED_URI, stringReplace(value, ATTR_WHITESPACE, ''))\n    ) {\n      // This attribute is safe\n      /* Keep image data URIs alive if src/xlink:href is allowed */\n      /* Further prevent gadget XSS for dynamically built script tags */\n    } else if (\n      (lcName === 'src' || lcName === 'xlink:href' || lcName === 'href') &&\n      lcTag !== 'script' &&\n      stringIndexOf(value, 'data:') === 0 &&\n      DATA_URI_TAGS[lcTag]\n    ) {\n      // This attribute is safe\n      /* Allow unknown protocols: This provides support for links that\n        are handled by protocol handlers which may be unknown ahead of\n        time, e.g. fb:, spotify: */\n    } else if (\n      ALLOW_UNKNOWN_PROTOCOLS &&\n      !regExpTest(IS_SCRIPT_OR_DATA, stringReplace(value, ATTR_WHITESPACE, ''))\n    ) {\n      // This attribute is safe\n      /* Check for binary attributes */\n    } else if (value) {\n      return false;\n    } else {\n      // Binary attributes are safe at this point\n      /* Anything else, presume unsafe, do not add it back */\n    }\n\n    return true;\n  };\n\n  /**\n   * _basicCustomElementCheck\n   * checks if at least one dash is included in tagName, and it's not the first char\n   * for more sophisticated checking see https://github.com/sindresorhus/validate-element-name\n   * @param {string} tagName name of the tag of the node to sanitize\n   */\n  const _basicCustomElementTest = function (tagName) {\n    return tagName !== 'annotation-xml' && stringMatch(tagName, CUSTOM_ELEMENT);\n  };\n\n  /**\n   * _sanitizeAttributes\n   *\n   * @protect attributes\n   * @protect nodeName\n   * @protect removeAttribute\n   * @protect setAttribute\n   *\n   * @param  {Node} currentNode to sanitize\n   */\n  const _sanitizeAttributes = function (currentNode) {\n    let attr;\n    let value;\n    let lcName;\n    let l;\n    /* Execute a hook if present */\n    _executeHook('beforeSanitizeAttributes', currentNode, null);\n\n    const { attributes } = currentNode;\n\n    /* Check if we have attributes; if not we might have a text node */\n    if (!attributes || _isClobbered(currentNode)) {\n      return;\n    }\n\n    const hookEvent = {\n      attrName: '',\n      attrValue: '',\n      keepAttr: true,\n      allowedAttributes: ALLOWED_ATTR,\n    };\n    l = attributes.length;\n\n    /* Go backwards over all attributes; safely remove bad ones */\n    while (l--) {\n      attr = attributes[l];\n      const { name, namespaceURI } = attr;\n      value = name === 'value' ? attr.value : stringTrim(attr.value);\n      lcName = transformCaseFunc(name);\n\n      /* Execute a hook if present */\n      hookEvent.attrName = lcName;\n      hookEvent.attrValue = value;\n      hookEvent.keepAttr = true;\n      hookEvent.forceKeepAttr = undefined; // Allows developers to see this is a property they can set\n      _executeHook('uponSanitizeAttribute', currentNode, hookEvent);\n      value = hookEvent.attrValue;\n\n      /* Did the hooks approve of the attribute? */\n      if (hookEvent.forceKeepAttr) {\n        continue;\n      }\n\n      /* Remove attribute */\n      _removeAttribute(name, currentNode);\n\n      /* Did the hooks approve of the attribute? */\n      if (!hookEvent.keepAttr) {\n        continue;\n      }\n\n      /* Work around a security issue in jQuery 3.0 */\n      if (!ALLOW_SELF_CLOSE_IN_ATTR && regExpTest(/\\/>/i, value)) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n\n      /* Sanitize attribute content to be template-safe */\n      if (SAFE_FOR_TEMPLATES) {\n        value = stringReplace(value, MUSTACHE_EXPR, ' ');\n        value = stringReplace(value, ERB_EXPR, ' ');\n        value = stringReplace(value, TMPLIT_EXPR, ' ');\n      }\n\n      /* Is `value` valid for this attribute? */\n      const lcTag = transformCaseFunc(currentNode.nodeName);\n      if (!_isValidAttribute(lcTag, lcName, value)) {\n        continue;\n      }\n\n      /* Full DOM Clobbering protection via namespace isolation,\n       * Prefix id and name attributes with `user-content-`\n       */\n      if (SANITIZE_NAMED_PROPS && (lcName === 'id' || lcName === 'name')) {\n        // Remove the attribute with this value\n        _removeAttribute(name, currentNode);\n\n        // Prefix the value and later re-create the attribute with the sanitized value\n        value = SANITIZE_NAMED_PROPS_PREFIX + value;\n      }\n\n      /* Work around a security issue with comments inside attributes */\n      if (SAFE_FOR_XML && regExpTest(/((--!?|])>)|<\\/(style|title)/i, value)) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n\n      /* Handle attributes that require Trusted Types */\n      if (\n        trustedTypesPolicy &&\n        typeof trustedTypes === 'object' &&\n        typeof trustedTypes.getAttributeType === 'function'\n      ) {\n        if (namespaceURI) {\n          /* Namespaces are not yet supported, see https://bugs.chromium.org/p/chromium/issues/detail?id=1305293 */\n        } else {\n          switch (trustedTypes.getAttributeType(lcTag, lcName)) {\n            case 'TrustedHTML': {\n              value = trustedTypesPolicy.createHTML(value);\n              break;\n            }\n\n            case 'TrustedScriptURL': {\n              value = trustedTypesPolicy.createScriptURL(value);\n              break;\n            }\n\n            default: {\n              break;\n            }\n          }\n        }\n      }\n\n      /* Handle invalid data-* attribute set by try-catching it */\n      try {\n        if (namespaceURI) {\n          currentNode.setAttributeNS(namespaceURI, name, value);\n        } else {\n          /* Fallback to setAttribute() for browser-unrecognized namespaces e.g. \"x-schema\". */\n          currentNode.setAttribute(name, value);\n        }\n\n        if (_isClobbered(currentNode)) {\n          _forceRemove(currentNode);\n        } else {\n          arrayPop(DOMPurify.removed);\n        }\n      } catch (_) {}\n    }\n\n    /* Execute a hook if present */\n    _executeHook('afterSanitizeAttributes', currentNode, null);\n  };\n\n  /**\n   * _sanitizeShadowDOM\n   *\n   * @param  {DocumentFragment} fragment to iterate over recursively\n   */\n  const _sanitizeShadowDOM = function (fragment) {\n    let shadowNode;\n    const shadowIterator = _createIterator(fragment);\n\n    /* Execute a hook if present */\n    _executeHook('beforeSanitizeShadowDOM', fragment, null);\n\n    while ((shadowNode = shadowIterator.nextNode())) {\n      /* Execute a hook if present */\n      _executeHook('uponSanitizeShadowNode', shadowNode, null);\n      /* Sanitize tags and elements */\n      _sanitizeElements(shadowNode);\n\n      /* Check attributes next */\n      _sanitizeAttributes(shadowNode);\n\n      /* Deep shadow DOM detected */\n      if (shadowNode.content instanceof DocumentFragment) {\n        _sanitizeShadowDOM(shadowNode.content);\n      }\n    }\n\n    /* Execute a hook if present */\n    _executeHook('afterSanitizeShadowDOM', fragment, null);\n  };\n\n  /**\n   * Sanitize\n   * Public method providing core sanitation functionality\n   *\n   * @param {String|Node} dirty string or DOM node\n   * @param {Object} configuration object\n   */\n  // eslint-disable-next-line complexity\n  DOMPurify.sanitize = function (dirty, cfg = {}) {\n    let body;\n    let importedNode;\n    let currentNode;\n    let oldNode;\n    let returnNode;\n    /* Make sure we have a string to sanitize.\n      DO NOT return early, as this will return the wrong type if\n      the user has requested a DOM object rather than a string */\n    IS_EMPTY_INPUT = !dirty;\n    if (IS_EMPTY_INPUT) {\n      dirty = '<!-->';\n    }\n\n    /* Stringify, in case dirty is an object */\n    if (typeof dirty !== 'string' && !_isNode(dirty)) {\n      if (typeof dirty.toString === 'function') {\n        dirty = dirty.toString();\n        if (typeof dirty !== 'string') {\n          throw typeErrorCreate('dirty is not a string, aborting');\n        }\n      } else {\n        throw typeErrorCreate('toString is not a function');\n      }\n    }\n\n    /* Check we can run. Otherwise fall back or ignore */\n    if (!DOMPurify.isSupported) {\n      if (\n        typeof window.toStaticHTML === 'object' ||\n        typeof window.toStaticHTML === 'function'\n      ) {\n        if (typeof dirty === 'string') {\n          return window.toStaticHTML(dirty);\n        }\n\n        if (_isNode(dirty)) {\n          return window.toStaticHTML(dirty.outerHTML);\n        }\n      }\n\n      return dirty;\n    }\n\n    /* Assign config vars */\n    if (!SET_CONFIG) {\n      _parseConfig(cfg);\n    }\n\n    /* Clean up removed elements */\n    DOMPurify.removed = [];\n\n    /* Check if dirty is correctly typed for IN_PLACE */\n    if (typeof dirty === 'string') {\n      IN_PLACE = false;\n    }\n\n    if (IN_PLACE) {\n      /* Do some early pre-sanitization to avoid unsafe root nodes */\n      if (dirty.nodeName) {\n        const tagName = transformCaseFunc(dirty.nodeName);\n        if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n          throw typeErrorCreate(\n            'root node is forbidden and cannot be sanitized in-place'\n          );\n        }\n      }\n    } else if (dirty instanceof Node) {\n      /* If dirty is a DOM element, append to an empty document to avoid\n         elements being stripped by the parser */\n      body = _initDocument('<!---->');\n      importedNode = body.ownerDocument.importNode(dirty, true);\n      if (importedNode.nodeType === 1 && importedNode.nodeName === 'BODY') {\n        /* Node is already a body, use as is */\n        body = importedNode;\n      } else if (importedNode.nodeName === 'HTML') {\n        body = importedNode;\n      } else {\n        // eslint-disable-next-line unicorn/prefer-dom-node-append\n        body.appendChild(importedNode);\n      }\n    } else {\n      /* Exit directly if we have nothing to do */\n      if (\n        !RETURN_DOM &&\n        !SAFE_FOR_TEMPLATES &&\n        !WHOLE_DOCUMENT &&\n        // eslint-disable-next-line unicorn/prefer-includes\n        dirty.indexOf('<') === -1\n      ) {\n        return trustedTypesPolicy && RETURN_TRUSTED_TYPE\n          ? trustedTypesPolicy.createHTML(dirty)\n          : dirty;\n      }\n\n      /* Initialize the document to work on */\n      body = _initDocument(dirty);\n\n      /* Check we have a DOM node from the data */\n      if (!body) {\n        return RETURN_DOM ? null : RETURN_TRUSTED_TYPE ? emptyHTML : '';\n      }\n    }\n\n    /* Remove first element node (ours) if FORCE_BODY is set */\n    if (body && FORCE_BODY) {\n      _forceRemove(body.firstChild);\n    }\n\n    /* Get node iterator */\n    const nodeIterator = _createIterator(IN_PLACE ? dirty : body);\n\n    /* Now start iterating over the created document */\n    while ((currentNode = nodeIterator.nextNode())) {\n      /* Fix IE's strange behavior with manipulated textNodes #89 */\n      if (currentNode.nodeType === 3 && currentNode === oldNode) {\n        continue;\n      }\n\n      /* Sanitize tags and elements */\n      _sanitizeElements(currentNode);\n\n      /* Check attributes next */\n      _sanitizeAttributes(currentNode);\n\n      /* Shadow DOM detected, sanitize it */\n      if (currentNode.content instanceof DocumentFragment) {\n        _sanitizeShadowDOM(currentNode.content);\n      }\n\n      oldNode = currentNode;\n    }\n\n    oldNode = null;\n\n    /* If we sanitized `dirty` in-place, return it. */\n    if (IN_PLACE) {\n      return dirty;\n    }\n\n    /* Return sanitized string or DOM */\n    if (RETURN_DOM) {\n      if (RETURN_DOM_FRAGMENT) {\n        returnNode = createDocumentFragment.call(body.ownerDocument);\n\n        while (body.firstChild) {\n          // eslint-disable-next-line unicorn/prefer-dom-node-append\n          returnNode.appendChild(body.firstChild);\n        }\n      } else {\n        returnNode = body;\n      }\n\n      if (ALLOWED_ATTR.shadowroot || ALLOWED_ATTR.shadowrootmod) {\n        /*\n          AdoptNode() is not used because internal state is not reset\n          (e.g. the past names map of a HTMLFormElement), this is safe\n          in theory but we would rather not risk another attack vector.\n          The state that is cloned by importNode() is explicitly defined\n          by the specs.\n        */\n        returnNode = importNode.call(originalDocument, returnNode, true);\n      }\n\n      return returnNode;\n    }\n\n    let serializedHTML = WHOLE_DOCUMENT ? body.outerHTML : body.innerHTML;\n\n    /* Serialize doctype if allowed */\n    if (\n      WHOLE_DOCUMENT &&\n      ALLOWED_TAGS['!doctype'] &&\n      body.ownerDocument &&\n      body.ownerDocument.doctype &&\n      body.ownerDocument.doctype.name &&\n      regExpTest(EXPRESSIONS.DOCTYPE_NAME, body.ownerDocument.doctype.name)\n    ) {\n      serializedHTML =\n        '<!DOCTYPE ' + body.ownerDocument.doctype.name + '>\\n' + serializedHTML;\n    }\n\n    /* Sanitize final string template-safe */\n    if (SAFE_FOR_TEMPLATES) {\n      serializedHTML = stringReplace(serializedHTML, MUSTACHE_EXPR, ' ');\n      serializedHTML = stringReplace(serializedHTML, ERB_EXPR, ' ');\n      serializedHTML = stringReplace(serializedHTML, TMPLIT_EXPR, ' ');\n    }\n\n    return trustedTypesPolicy && RETURN_TRUSTED_TYPE\n      ? trustedTypesPolicy.createHTML(serializedHTML)\n      : serializedHTML;\n  };\n\n  /**\n   * Public method to set the configuration once\n   * setConfig\n   *\n   * @param {Object} cfg configuration object\n   */\n  DOMPurify.setConfig = function (cfg) {\n    _parseConfig(cfg);\n    SET_CONFIG = true;\n  };\n\n  /**\n   * Public method to remove the configuration\n   * clearConfig\n   *\n   */\n  DOMPurify.clearConfig = function () {\n    CONFIG = null;\n    SET_CONFIG = false;\n  };\n\n  /**\n   * Public method to check if an attribute value is valid.\n   * Uses last set config, if any. Otherwise, uses config defaults.\n   * isValidAttribute\n   *\n   * @param  {string} tag Tag name of containing element.\n   * @param  {string} attr Attribute name.\n   * @param  {string} value Attribute value.\n   * @return {Boolean} Returns true if `value` is valid. Otherwise, returns false.\n   */\n  DOMPurify.isValidAttribute = function (tag, attr, value) {\n    /* Initialize shared config vars if necessary. */\n    if (!CONFIG) {\n      _parseConfig({});\n    }\n\n    const lcTag = transformCaseFunc(tag);\n    const lcName = transformCaseFunc(attr);\n    return _isValidAttribute(lcTag, lcName, value);\n  };\n\n  /**\n   * AddHook\n   * Public method to add DOMPurify hooks\n   *\n   * @param {String} entryPoint entry point for the hook to add\n   * @param {Function} hookFunction function to execute\n   */\n  DOMPurify.addHook = function (entryPoint, hookFunction) {\n    if (typeof hookFunction !== 'function') {\n      return;\n    }\n\n    hooks[entryPoint] = hooks[entryPoint] || [];\n    arrayPush(hooks[entryPoint], hookFunction);\n  };\n\n  /**\n   * RemoveHook\n   * Public method to remove a DOMPurify hook at a given entryPoint\n   * (pops it from the stack of hooks if more are present)\n   *\n   * @param {String} entryPoint entry point for the hook to remove\n   * @return {Function} removed(popped) hook\n   */\n  DOMPurify.removeHook = function (entryPoint) {\n    if (hooks[entryPoint]) {\n      return arrayPop(hooks[entryPoint]);\n    }\n  };\n\n  /**\n   * RemoveHooks\n   * Public method to remove all DOMPurify hooks at a given entryPoint\n   *\n   * @param  {String} entryPoint entry point for the hooks to remove\n   */\n  DOMPurify.removeHooks = function (entryPoint) {\n    if (hooks[entryPoint]) {\n      hooks[entryPoint] = [];\n    }\n  };\n\n  /**\n   * RemoveAllHooks\n   * Public method to remove all DOMPurify hooks\n   *\n   */\n  DOMPurify.removeAllHooks = function () {\n    hooks = {};\n  };\n\n  return DOMPurify;\n}\n\nexport default createDOMPurify();\n"], "names": ["hasOwnProperty", "Object", "setPrototypeOf", "isFrozen", "getPrototypeOf", "getOwnPropertyDescriptor", "freeze", "seal", "create", "_ref", "Reflect", "apply", "construct", "fun", "thisValue", "args", "x", "Func", "_construct", "_toConsumableArray", "arrayForEach", "unapply", "Array", "prototype", "for<PERSON>ach", "arrayPop", "pop", "arrayPush", "push", "stringToLowerCase", "String", "toLowerCase", "stringToString", "toString", "stringMatch", "match", "stringReplace", "replace", "stringIndexOf", "indexOf", "stringTrim", "trim", "regExpTest", "RegExp", "test", "typeErrorCreate", "unconstruct", "TypeError", "func", "thisArg", "_len", "arguments", "length", "_key", "_len2", "_key2", "addToSet", "set", "array", "transformCaseFunc", "_transformCaseFunc", "l", "element", "lcElement", "clone", "object", "newObject", "property", "lookupGetter", "prop", "desc", "get", "value", "fallback<PERSON><PERSON><PERSON>", "console", "warn", "html", "svg", "svgFilters", "svgDisallowed", "mathMl", "mathMlDisallowed", "text", "xml", "MUSTACHE_EXPR", "ERB_EXPR", "TMPLIT_EXPR", "DATA_ATTR", "ARIA_ATTR", "IS_ALLOWED_URI", "IS_SCRIPT_OR_DATA", "ATTR_WHITESPACE", "DOCTYPE_NAME", "CUSTOM_ELEMENT", "getGlobal", "window", "_createTrustedTypesPolicy", "trustedTypes", "document", "_typeof", "createPolicy", "suffix", "ATTR_NAME", "currentScript", "hasAttribute", "getAttribute", "policyName", "createHTML", "createScriptURL", "scriptUrl", "_", "createDOMPurify", "undefined", "DOMPurify", "root", "version", "VERSION", "removed", "nodeType", "isSupported", "originalDocument", "DocumentFragment", "HTMLTemplateElement", "Node", "Element", "Node<PERSON><PERSON><PERSON>", "_window$NamedNodeMap", "NamedNodeMap", "MozNamedAttrMap", "HTMLFormElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ElementPrototype", "cloneNode", "getNextSibling", "getChildNodes", "getParentNode", "template", "createElement", "content", "ownerDocument", "trustedTypesPolicy", "emptyHTML", "_document", "implementation", "createNodeIterator", "createDocumentFragment", "getElementsByTagName", "importNode", "documentMode", "hooks", "createHTMLDocument", "EXPRESSIONS", "ALLOWED_TAGS", "DEFAULT_ALLOWED_TAGS", "concat", "TAGS", "ALLOWED_ATTR", "DEFAULT_ALLOWED_ATTR", "ATTRS", "CUSTOM_ELEMENT_HANDLING", "tagNameCheck", "writable", "configurable", "enumerable", "attributeNameCheck", "allowCustomizedBuiltInElements", "FORBID_TAGS", "FORBID_ATTR", "ALLOW_ARIA_ATTR", "ALLOW_DATA_ATTR", "ALLOW_UNKNOWN_PROTOCOLS", "ALLOW_SELF_CLOSE_IN_ATTR", "SAFE_FOR_TEMPLATES", "SAFE_FOR_XML", "WHOLE_DOCUMENT", "SET_CONFIG", "FORCE_BODY", "RETURN_DOM", "RETURN_DOM_FRAGMENT", "RETURN_TRUSTED_TYPE", "SANITIZE_DOM", "SANITIZE_NAMED_PROPS", "SANITIZE_NAMED_PROPS_PREFIX", "KEEP_CONTENT", "IN_PLACE", "USE_PROFILES", "FORBID_CONTENTS", "DEFAULT_FORBID_CONTENTS", "DATA_URI_TAGS", "DEFAULT_DATA_URI_TAGS", "URI_SAFE_ATTRIBUTES", "DEFAULT_URI_SAFE_ATTRIBUTES", "MATHML_NAMESPACE", "SVG_NAMESPACE", "HTML_NAMESPACE", "NAMESPACE", "IS_EMPTY_INPUT", "ALLOWED_NAMESPACES", "DEFAULT_ALLOWED_NAMESPACES", "PARSER_MEDIA_TYPE", "SUPPORTED_PARSER_MEDIA_TYPES", "DEFAULT_PARSER_MEDIA_TYPE", "CONFIG", "formElement", "isRegexOrFunction", "testValue", "Function", "_parseConfig", "cfg", "ADD_URI_SAFE_ATTR", "ADD_DATA_URI_TAGS", "ALLOWED_URI_REGEXP", "ADD_TAGS", "ADD_ATTR", "table", "tbody", "MATHML_TEXT_INTEGRATION_POINTS", "HTML_INTEGRATION_POINTS", "COMMON_SVG_AND_HTML_ELEMENTS", "ALL_SVG_TAGS", "ALL_MATHML_TAGS", "_checkValidNamespace", "parent", "tagName", "namespaceURI", "parentTagName", "Boolean", "_forceRemove", "node", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "outerHTML", "remove", "_removeAttribute", "name", "attribute", "getAttributeNode", "from", "removeAttribute", "setAttribute", "_initDocument", "dirty", "doc", "leadingWhitespace", "matches", "dirtyPayload", "parseFromString", "documentElement", "createDocument", "innerHTML", "body", "insertBefore", "createTextNode", "childNodes", "call", "_createIterator", "SHOW_ELEMENT", "SHOW_COMMENT", "SHOW_TEXT", "SHOW_PROCESSING_INSTRUCTION", "SHOW_CDATA_SECTION", "_isClobbered", "elm", "nodeName", "textContent", "attributes", "hasChildNodes", "_isNode", "_executeHook", "entryPoint", "currentNode", "data", "hook", "_sanitizeElements", "allowedTags", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "_basicCustomElementTest", "childCount", "i", "child<PERSON>lone", "__removalCount", "_isValidAttribute", "lcTag", "lcName", "_sanitizeAttributes", "attr", "hookEvent", "attrName", "attrValue", "keepAttr", "allowedAttributes", "_attr", "forceKeepAttr", "getAttributeType", "setAttributeNS", "_sanitizeShadowDOM", "fragment", "shadowNode", "shadowIterator", "nextNode", "sanitize", "importedNode", "oldNode", "returnNode", "toStaticHTML", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "nodeIterator", "shadowroot", "shadowrootmod", "serializedHTML", "doctype", "setConfig", "clearConfig", "isValidAttribute", "tag", "addHook", "hookFunction", "removeH<PERSON>", "removeHooks", "removeAllHooks"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IACEA,cAAc,GAKZC,MAAM,CALRD,cAAc;EACdE,cAAc,GAIZD,MAAM,CAJRC,cAAc;EACdC,QAAQ,GAGNF,MAAM,CAHRE,QAAQ;EACRC,cAAc,GAEZH,MAAM,CAFRG,cAAc;EACdC,wBAAwB,GACtBJ,MAAM,CADRI,wBAAwB,CAAA;AAG1B,IAAMC,MAAM,GAAmBL,MAAM,CAA/BK,MAAM;EAAEC,IAAI,GAAaN,MAAM,CAAvBM,IAAI;AAAEC,EAAAA,MAAM,GAAKP,MAAM,CAAjBO,MAAM,CAAY;AACtC,IAAAC,IAAA,GAA2B,OAAOC,OAAO,KAAK,WAAW,IAAIA,OAAO;EAA9DC,KAAK,GAAAF,IAAA,CAALE,KAAK;EAAEC,SAAS,GAAAH,IAAA,CAATG,SAAS,CAAA;AAEtB,IAAI,CAACD,KAAK,EAAE;EACVA,KAAK,GAAG,SAAAA,KAAUE,CAAAA,GAAG,EAAEC,SAAS,EAAEC,IAAI,EAAE;AACtC,IAAA,OAAOF,GAAG,CAACF,KAAK,CAACG,SAAS,EAAEC,IAAI,CAAC,CAAA;GAClC,CAAA;AACH,CAAA;AAEA,IAAI,CAACT,MAAM,EAAE;AACXA,EAAAA,MAAM,GAAG,SAAAA,MAAUU,CAAAA,CAAC,EAAE;AACpB,IAAA,OAAOA,CAAC,CAAA;GACT,CAAA;AACH,CAAA;AAEA,IAAI,CAACT,IAAI,EAAE;AACTA,EAAAA,IAAI,GAAG,SAAAA,IAAUS,CAAAA,CAAC,EAAE;AAClB,IAAA,OAAOA,CAAC,CAAA;GACT,CAAA;AACH,CAAA;AAEA,IAAI,CAACJ,SAAS,EAAE;AACdA,EAAAA,SAAS,GAAG,SAAAA,SAAAA,CAAUK,IAAI,EAAEF,IAAI,EAAE;AAChC,IAAA,OAAAG,UAAA,CAAWD,IAAI,EAAAE,kBAAA,CAAIJ,IAAI,CAAA,CAAA,CAAA;GACxB,CAAA;AACH,CAAA;AAEA,IAAMK,YAAY,GAAGC,OAAO,CAACC,KAAK,CAACC,SAAS,CAACC,OAAO,CAAC,CAAA;AAErD,IAAMC,QAAQ,GAAGJ,OAAO,CAACC,KAAK,CAACC,SAAS,CAACG,GAAG,CAAC,CAAA;AAC7C,IAAMC,SAAS,GAAGN,OAAO,CAACC,KAAK,CAACC,SAAS,CAACK,IAAI,CAAC,CAAA;AAG/C,IAAMC,iBAAiB,GAAGR,OAAO,CAACS,MAAM,CAACP,SAAS,CAACQ,WAAW,CAAC,CAAA;AAC/D,IAAMC,cAAc,GAAGX,OAAO,CAACS,MAAM,CAACP,SAAS,CAACU,QAAQ,CAAC,CAAA;AACzD,IAAMC,WAAW,GAAGb,OAAO,CAACS,MAAM,CAACP,SAAS,CAACY,KAAK,CAAC,CAAA;AACnD,IAAMC,aAAa,GAAGf,OAAO,CAACS,MAAM,CAACP,SAAS,CAACc,OAAO,CAAC,CAAA;AACvD,IAAMC,aAAa,GAAGjB,OAAO,CAACS,MAAM,CAACP,SAAS,CAACgB,OAAO,CAAC,CAAA;AACvD,IAAMC,UAAU,GAAGnB,OAAO,CAACS,MAAM,CAACP,SAAS,CAACkB,IAAI,CAAC,CAAA;AAEjD,IAAMC,UAAU,GAAGrB,OAAO,CAACsB,MAAM,CAACpB,SAAS,CAACqB,IAAI,CAAC,CAAA;AAEjD,IAAMC,eAAe,GAAGC,WAAW,CAACC,SAAS,CAAC,CAAA;AAEvC,SAAS1B,OAAOA,CAAC2B,IAAI,EAAE;AAC5B,EAAA,OAAO,UAACC,OAAO,EAAA;IAAA,KAAAC,IAAAA,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAKrC,IAAI,OAAAO,KAAA,CAAA4B,IAAA,GAAAA,CAAAA,GAAAA,IAAA,WAAAG,IAAA,GAAA,CAAA,EAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA,EAAA,EAAA;AAAJtC,MAAAA,IAAI,CAAAsC,IAAA,GAAAF,CAAAA,CAAAA,GAAAA,SAAA,CAAAE,IAAA,CAAA,CAAA;AAAA,KAAA;AAAA,IAAA,OAAK1C,KAAK,CAACqC,IAAI,EAAEC,OAAO,EAAElC,IAAI,CAAC,CAAA;AAAA,GAAA,CAAA;AACzD,CAAA;AAEO,SAAS+B,WAAWA,CAACE,IAAI,EAAE;EAChC,OAAO,YAAA;AAAA,IAAA,KAAA,IAAAM,KAAA,GAAAH,SAAA,CAAAC,MAAA,EAAIrC,IAAI,GAAAO,IAAAA,KAAA,CAAAgC,KAAA,GAAAC,KAAA,GAAA,CAAA,EAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA,EAAA,EAAA;AAAJxC,MAAAA,IAAI,CAAAwC,KAAA,CAAAJ,GAAAA,SAAA,CAAAI,KAAA,CAAA,CAAA;AAAA,KAAA;AAAA,IAAA,OAAK3C,SAAS,CAACoC,IAAI,EAAEjC,IAAI,CAAC,CAAA;AAAA,GAAA,CAAA;AAC3C,CAAA;;AAEA;AACO,SAASyC,QAAQA,CAACC,GAAG,EAAEC,KAAK,EAAEC,iBAAiB,EAAE;AAAA,EAAA,IAAAC,kBAAA,CAAA;EACtDD,iBAAiB,GAAA,CAAAC,kBAAA,GAAGD,iBAAiB,cAAAC,kBAAA,KAAA,KAAA,CAAA,GAAAA,kBAAA,GAAI/B,iBAAiB,CAAA;AAC1D,EAAA,IAAI3B,cAAc,EAAE;AAClB;AACA;AACA;AACAA,IAAAA,cAAc,CAACuD,GAAG,EAAE,IAAI,CAAC,CAAA;AAC3B,GAAA;AAEA,EAAA,IAAII,CAAC,GAAGH,KAAK,CAACN,MAAM,CAAA;EACpB,OAAOS,CAAC,EAAE,EAAE;AACV,IAAA,IAAIC,OAAO,GAAGJ,KAAK,CAACG,CAAC,CAAC,CAAA;AACtB,IAAA,IAAI,OAAOC,OAAO,KAAK,QAAQ,EAAE;AAC/B,MAAA,IAAMC,SAAS,GAAGJ,iBAAiB,CAACG,OAAO,CAAC,CAAA;MAC5C,IAAIC,SAAS,KAAKD,OAAO,EAAE;AACzB;AACA,QAAA,IAAI,CAAC3D,QAAQ,CAACuD,KAAK,CAAC,EAAE;AACpBA,UAAAA,KAAK,CAACG,CAAC,CAAC,GAAGE,SAAS,CAAA;AACtB,SAAA;AAEAD,QAAAA,OAAO,GAAGC,SAAS,CAAA;AACrB,OAAA;AACF,KAAA;AAEAN,IAAAA,GAAG,CAACK,OAAO,CAAC,GAAG,IAAI,CAAA;AACrB,GAAA;AAEA,EAAA,OAAOL,GAAG,CAAA;AACZ,CAAA;;AAEA;AACO,SAASO,KAAKA,CAACC,MAAM,EAAE;AAC5B,EAAA,IAAMC,SAAS,GAAG1D,MAAM,CAAC,IAAI,CAAC,CAAA;AAE9B,EAAA,IAAI2D,QAAQ,CAAA;EACZ,KAAKA,QAAQ,IAAIF,MAAM,EAAE;AACvB,IAAA,IAAItD,KAAK,CAACX,cAAc,EAAEiE,MAAM,EAAE,CAACE,QAAQ,CAAC,CAAC,KAAK,IAAI,EAAE;AACtDD,MAAAA,SAAS,CAACC,QAAQ,CAAC,GAAGF,MAAM,CAACE,QAAQ,CAAC,CAAA;AACxC,KAAA;AACF,GAAA;AAEA,EAAA,OAAOD,SAAS,CAAA;AAClB,CAAA;;AAEA;AACA;AACA;AACA;AACA,SAASE,YAAYA,CAACH,MAAM,EAAEI,IAAI,EAAE;EAClC,OAAOJ,MAAM,KAAK,IAAI,EAAE;AACtB,IAAA,IAAMK,IAAI,GAAGjE,wBAAwB,CAAC4D,MAAM,EAAEI,IAAI,CAAC,CAAA;AACnD,IAAA,IAAIC,IAAI,EAAE;MACR,IAAIA,IAAI,CAACC,GAAG,EAAE;AACZ,QAAA,OAAOlD,OAAO,CAACiD,IAAI,CAACC,GAAG,CAAC,CAAA;AAC1B,OAAA;AAEA,MAAA,IAAI,OAAOD,IAAI,CAACE,KAAK,KAAK,UAAU,EAAE;AACpC,QAAA,OAAOnD,OAAO,CAACiD,IAAI,CAACE,KAAK,CAAC,CAAA;AAC5B,OAAA;AACF,KAAA;AAEAP,IAAAA,MAAM,GAAG7D,cAAc,CAAC6D,MAAM,CAAC,CAAA;AACjC,GAAA;EAEA,SAASQ,aAAaA,CAACX,OAAO,EAAE;AAC9BY,IAAAA,OAAO,CAACC,IAAI,CAAC,oBAAoB,EAAEb,OAAO,CAAC,CAAA;AAC3C,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;AAEA,EAAA,OAAOW,aAAa,CAAA;AACtB;;ACjIO,IAAMG,MAAI,GAAGtE,MAAM,CAAC,CACzB,GAAG,EACH,MAAM,EACN,SAAS,EACT,SAAS,EACT,MAAM,EACN,SAAS,EACT,OAAO,EACP,OAAO,EACP,GAAG,EACH,KAAK,EACL,KAAK,EACL,KAAK,EACL,OAAO,EACP,YAAY,EACZ,MAAM,EACN,IAAI,EACJ,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,MAAM,EACN,MAAM,EACN,KAAK,EACL,UAAU,EACV,SAAS,EACT,MAAM,EACN,UAAU,EACV,IAAI,EACJ,WAAW,EACX,KAAK,EACL,SAAS,EACT,KAAK,EACL,QAAQ,EACR,KAAK,EACL,KAAK,EACL,IAAI,EACJ,IAAI,EACJ,SAAS,EACT,IAAI,EACJ,UAAU,EACV,YAAY,EACZ,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,MAAM,EACN,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,QAAQ,EACR,IAAI,EACJ,MAAM,EACN,GAAG,EACH,KAAK,EACL,OAAO,EACP,KAAK,EACL,KAAK,EACL,OAAO,EACP,QAAQ,EACR,IAAI,EACJ,MAAM,EACN,KAAK,EACL,MAAM,EACN,SAAS,EACT,MAAM,EACN,UAAU,EACV,OAAO,EACP,KAAK,EACL,MAAM,EACN,IAAI,EACJ,UAAU,EACV,QAAQ,EACR,QAAQ,EACR,GAAG,EACH,SAAS,EACT,KAAK,EACL,UAAU,EACV,GAAG,EACH,IAAI,EACJ,IAAI,EACJ,MAAM,EACN,GAAG,EACH,MAAM,EACN,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,KAAK,EACL,SAAS,EACT,KAAK,EACL,OAAO,EACP,OAAO,EACP,IAAI,EACJ,UAAU,EACV,UAAU,EACV,OAAO,EACP,IAAI,EACJ,OAAO,EACP,MAAM,EACN,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,KAAK,EACL,OAAO,EACP,KAAK,CACN,CAAC,CAAA;;AAEF;AACO,IAAMuE,KAAG,GAAGvE,MAAM,CAAC,CACxB,KAAK,EACL,GAAG,EACH,UAAU,EACV,aAAa,EACb,cAAc,EACd,cAAc,EACd,eAAe,EACf,kBAAkB,EAClB,QAAQ,EACR,UAAU,EACV,MAAM,EACN,MAAM,EACN,SAAS,EACT,QAAQ,EACR,MAAM,EACN,GAAG,EACH,OAAO,EACP,UAAU,EACV,OAAO,EACP,OAAO,EACP,MAAM,EACN,gBAAgB,EAChB,QAAQ,EACR,MAAM,EACN,UAAU,EACV,OAAO,EACP,MAAM,EACN,SAAS,EACT,SAAS,EACT,UAAU,EACV,gBAAgB,EAChB,MAAM,EACN,MAAM,EACN,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,UAAU,EACV,OAAO,EACP,MAAM,EACN,OAAO,EACP,MAAM,EACN,OAAO,CACR,CAAC,CAAA;AAEK,IAAMwE,UAAU,GAAGxE,MAAM,CAAC,CAC/B,SAAS,EACT,eAAe,EACf,qBAAqB,EACrB,aAAa,EACb,kBAAkB,EAClB,mBAAmB,EACnB,mBAAmB,EACnB,gBAAgB,EAChB,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,gBAAgB,EAChB,SAAS,EACT,SAAS,EACT,aAAa,EACb,cAAc,EACd,UAAU,EACV,cAAc,EACd,oBAAoB,EACpB,aAAa,EACb,QAAQ,EACR,cAAc,CACf,CAAC,CAAA;;AAEF;AACA;AACA;AACA;AACO,IAAMyE,aAAa,GAAGzE,MAAM,CAAC,CAClC,SAAS,EACT,eAAe,EACf,QAAQ,EACR,SAAS,EACT,cAAc,EACd,WAAW,EACX,kBAAkB,EAClB,gBAAgB,EAChB,eAAe,EACf,eAAe,EACf,eAAe,EACf,OAAO,EACP,WAAW,EACX,MAAM,EACN,cAAc,EACd,WAAW,EACX,SAAS,EACT,eAAe,EACf,QAAQ,EACR,KAAK,EACL,YAAY,EACZ,SAAS,EACT,KAAK,CACN,CAAC,CAAA;AAEK,IAAM0E,QAAM,GAAG1E,MAAM,CAAC,CAC3B,MAAM,EACN,UAAU,EACV,QAAQ,EACR,SAAS,EACT,OAAO,EACP,QAAQ,EACR,IAAI,EACJ,YAAY,EACZ,eAAe,EACf,IAAI,EACJ,IAAI,EACJ,OAAO,EACP,SAAS,EACT,UAAU,EACV,OAAO,EACP,MAAM,EACN,IAAI,EACJ,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,MAAM,EACN,MAAM,EACN,SAAS,EACT,QAAQ,EACR,KAAK,EACL,OAAO,EACP,KAAK,EACL,QAAQ,EACR,YAAY,CACb,CAAC,CAAA;;AAEF;AACA;AACO,IAAM2E,gBAAgB,GAAG3E,MAAM,CAAC,CACrC,SAAS,EACT,aAAa,EACb,YAAY,EACZ,UAAU,EACV,WAAW,EACX,SAAS,EACT,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,WAAW,EACX,YAAY,EACZ,gBAAgB,EAChB,aAAa,EACb,MAAM,CACP,CAAC,CAAA;AAEK,IAAM4E,IAAI,GAAG5E,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC;;ACpR9B,IAAMsE,IAAI,GAAGtE,MAAM,CAAC,CACzB,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,KAAK,EACL,gBAAgB,EAChB,cAAc,EACd,sBAAsB,EACtB,UAAU,EACV,YAAY,EACZ,SAAS,EACT,QAAQ,EACR,SAAS,EACT,aAAa,EACb,aAAa,EACb,SAAS,EACT,MAAM,EACN,OAAO,EACP,OAAO,EACP,OAAO,EACP,MAAM,EACN,SAAS,EACT,UAAU,EACV,cAAc,EACd,QAAQ,EACR,aAAa,EACb,UAAU,EACV,UAAU,EACV,SAAS,EACT,KAAK,EACL,UAAU,EACV,yBAAyB,EACzB,uBAAuB,EACvB,UAAU,EACV,WAAW,EACX,SAAS,EACT,cAAc,EACd,MAAM,EACN,KAAK,EACL,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,MAAM,EACN,UAAU,EACV,IAAI,EACJ,WAAW,EACX,WAAW,EACX,OAAO,EACP,MAAM,EACN,OAAO,EACP,MAAM,EACN,MAAM,EACN,SAAS,EACT,MAAM,EACN,KAAK,EACL,KAAK,EACL,WAAW,EACX,OAAO,EACP,QAAQ,EACR,KAAK,EACL,WAAW,EACX,UAAU,EACV,OAAO,EACP,MAAM,EACN,OAAO,EACP,SAAS,EACT,YAAY,EACZ,QAAQ,EACR,MAAM,EACN,SAAS,EACT,SAAS,EACT,aAAa,EACb,aAAa,EACb,QAAQ,EACR,SAAS,EACT,SAAS,EACT,YAAY,EACZ,UAAU,EACV,KAAK,EACL,UAAU,EACV,KAAK,EACL,UAAU,EACV,MAAM,EACN,MAAM,EACN,SAAS,EACT,YAAY,EACZ,OAAO,EACP,UAAU,EACV,OAAO,EACP,MAAM,EACN,OAAO,EACP,MAAM,EACN,SAAS,EACT,OAAO,EACP,KAAK,EACL,QAAQ,EACR,MAAM,EACN,OAAO,EACP,SAAS,EACT,UAAU,EACV,OAAO,EACP,WAAW,EACX,MAAM,EACN,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,OAAO,EACP,OAAO,EACP,MAAM,CACP,CAAC,CAAA;AAEK,IAAMuE,GAAG,GAAGvE,MAAM,CAAC,CACxB,eAAe,EACf,YAAY,EACZ,UAAU,EACV,oBAAoB,EACpB,QAAQ,EACR,eAAe,EACf,eAAe,EACf,SAAS,EACT,eAAe,EACf,gBAAgB,EAChB,OAAO,EACP,MAAM,EACN,IAAI,EACJ,OAAO,EACP,MAAM,EACN,eAAe,EACf,WAAW,EACX,WAAW,EACX,OAAO,EACP,qBAAqB,EACrB,6BAA6B,EAC7B,eAAe,EACf,iBAAiB,EACjB,IAAI,EACJ,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,IAAI,EACJ,iBAAiB,EACjB,WAAW,EACX,SAAS,EACT,SAAS,EACT,KAAK,EACL,UAAU,EACV,WAAW,EACX,KAAK,EACL,MAAM,EACN,cAAc,EACd,WAAW,EACX,QAAQ,EACR,aAAa,EACb,aAAa,EACb,eAAe,EACf,aAAa,EACb,WAAW,EACX,kBAAkB,EAClB,cAAc,EACd,YAAY,EACZ,cAAc,EACd,aAAa,EACb,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,YAAY,EACZ,UAAU,EACV,eAAe,EACf,mBAAmB,EACnB,QAAQ,EACR,MAAM,EACN,IAAI,EACJ,iBAAiB,EACjB,IAAI,EACJ,KAAK,EACL,GAAG,EACH,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,SAAS,EACT,WAAW,EACX,YAAY,EACZ,UAAU,EACV,MAAM,EACN,cAAc,EACd,gBAAgB,EAChB,cAAc,EACd,kBAAkB,EAClB,gBAAgB,EAChB,OAAO,EACP,YAAY,EACZ,YAAY,EACZ,cAAc,EACd,cAAc,EACd,aAAa,EACb,aAAa,EACb,kBAAkB,EAClB,WAAW,EACX,KAAK,EACL,MAAM,EACN,OAAO,EACP,QAAQ,EACR,MAAM,EACN,KAAK,EACL,MAAM,EACN,YAAY,EACZ,QAAQ,EACR,UAAU,EACV,SAAS,EACT,OAAO,EACP,QAAQ,EACR,aAAa,EACb,QAAQ,EACR,UAAU,EACV,aAAa,EACb,MAAM,EACN,YAAY,EACZ,qBAAqB,EACrB,kBAAkB,EAClB,cAAc,EACd,QAAQ,EACR,eAAe,EACf,qBAAqB,EACrB,gBAAgB,EAChB,GAAG,EACH,IAAI,EACJ,IAAI,EACJ,QAAQ,EACR,MAAM,EACN,MAAM,EACN,aAAa,EACb,WAAW,EACX,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,MAAM,EACN,iBAAiB,EACjB,kBAAkB,EAClB,kBAAkB,EAClB,cAAc,EACd,aAAa,EACb,cAAc,EACd,aAAa,EACb,YAAY,EACZ,cAAc,EACd,kBAAkB,EAClB,mBAAmB,EACnB,gBAAgB,EAChB,iBAAiB,EACjB,mBAAmB,EACnB,gBAAgB,EAChB,QAAQ,EACR,cAAc,EACd,OAAO,EACP,cAAc,EACd,gBAAgB,EAChB,UAAU,EACV,SAAS,EACT,SAAS,EACT,WAAW,EACX,kBAAkB,EAClB,aAAa,EACb,iBAAiB,EACjB,gBAAgB,EAChB,YAAY,EACZ,MAAM,EACN,IAAI,EACJ,IAAI,EACJ,SAAS,EACT,QAAQ,EACR,SAAS,EACT,YAAY,EACZ,SAAS,EACT,YAAY,EACZ,eAAe,EACf,eAAe,EACf,OAAO,EACP,cAAc,EACd,MAAM,EACN,cAAc,EACd,kBAAkB,EAClB,kBAAkB,EAClB,GAAG,EACH,IAAI,EACJ,IAAI,EACJ,OAAO,EACP,GAAG,EACH,IAAI,EACJ,IAAI,EACJ,GAAG,EACH,YAAY,CACb,CAAC,CAAA;AAEK,IAAM0E,MAAM,GAAG1E,MAAM,CAAC,CAC3B,QAAQ,EACR,aAAa,EACb,OAAO,EACP,UAAU,EACV,OAAO,EACP,cAAc,EACd,aAAa,EACb,YAAY,EACZ,YAAY,EACZ,OAAO,EACP,KAAK,EACL,SAAS,EACT,cAAc,EACd,UAAU,EACV,OAAO,EACP,OAAO,EACP,QAAQ,EACR,MAAM,EACN,IAAI,EACJ,SAAS,EACT,QAAQ,EACR,eAAe,EACf,QAAQ,EACR,QAAQ,EACR,gBAAgB,EAChB,WAAW,EACX,UAAU,EACV,aAAa,EACb,SAAS,EACT,SAAS,EACT,eAAe,EACf,UAAU,EACV,UAAU,EACV,MAAM,EACN,UAAU,EACV,UAAU,EACV,YAAY,EACZ,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,aAAa,EACb,eAAe,EACf,sBAAsB,EACtB,WAAW,EACX,WAAW,EACX,YAAY,EACZ,UAAU,EACV,gBAAgB,EAChB,gBAAgB,EAChB,WAAW,EACX,SAAS,EACT,OAAO,EACP,OAAO,CACR,CAAC,CAAA;AAEK,IAAM6E,GAAG,GAAG7E,MAAM,CAAC,CACxB,YAAY,EACZ,QAAQ,EACR,aAAa,EACb,WAAW,EACX,aAAa,CACd,CAAC;;ACvWF;AACO,IAAM8E,aAAa,GAAG7E,IAAI,CAAC,2BAA2B,CAAC,CAAC;AACxD,IAAM8E,QAAQ,GAAG9E,IAAI,CAAC,uBAAuB,CAAC,CAAA;AAC9C,IAAM+E,WAAW,GAAG/E,IAAI,CAAC,eAAe,CAAC,CAAA;AACzC,IAAMgF,SAAS,GAAGhF,IAAI,CAAC,8BAA8B,CAAC,CAAC;AACvD,IAAMiF,SAAS,GAAGjF,IAAI,CAAC,gBAAgB,CAAC,CAAC;AACzC,IAAMkF,cAAc,GAAGlF,IAAI,CAChC,uFAAuF;AACzF,CAAC,CAAA;AACM,IAAMmF,iBAAiB,GAAGnF,IAAI,CAAC,uBAAuB,CAAC,CAAA;AACvD,IAAMoF,eAAe,GAAGpF,IAAI,CACjC,6DAA6D;AAC/D,CAAC,CAAA;AACM,IAAMqF,YAAY,GAAGrF,IAAI,CAAC,SAAS,CAAC,CAAA;AACpC,IAAMsF,cAAc,GAAGtF,IAAI,CAAC,0BAA0B,CAAC;;ACK9D,IAAMuF,SAAS,GAAG,SAAZA,SAASA,GAAA;AAAA,EAAA,OAAU,OAAOC,MAAM,KAAK,WAAW,GAAG,IAAI,GAAGA,MAAM,CAAA;AAAA,CAAC,CAAA;;AAEvE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAMC,yBAAyB,GAAG,SAA5BA,yBAAyBA,CAAaC,YAAY,EAAEC,QAAQ,EAAE;AAClE,EAAA,IACEC,OAAA,CAAOF,YAAY,CAAA,KAAK,QAAQ,IAChC,OAAOA,YAAY,CAACG,YAAY,KAAK,UAAU,EAC/C;AACA,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;;AAEA;AACA;AACA;EACA,IAAIC,MAAM,GAAG,IAAI,CAAA;EACjB,IAAMC,SAAS,GAAG,uBAAuB,CAAA;AACzC,EAAA,IACEJ,QAAQ,CAACK,aAAa,IACtBL,QAAQ,CAACK,aAAa,CAACC,YAAY,CAACF,SAAS,CAAC,EAC9C;IACAD,MAAM,GAAGH,QAAQ,CAACK,aAAa,CAACE,YAAY,CAACH,SAAS,CAAC,CAAA;AACzD,GAAA;EAEA,IAAMI,UAAU,GAAG,WAAW,IAAIL,MAAM,GAAG,GAAG,GAAGA,MAAM,GAAG,EAAE,CAAC,CAAA;EAE7D,IAAI;AACF,IAAA,OAAOJ,YAAY,CAACG,YAAY,CAACM,UAAU,EAAE;MAC3CC,UAAU,EAAA,SAAAA,UAAC/B,CAAAA,IAAI,EAAE;AACf,QAAA,OAAOA,IAAI,CAAA;OACZ;MACDgC,eAAe,EAAA,SAAAA,eAACC,CAAAA,SAAS,EAAE;AACzB,QAAA,OAAOA,SAAS,CAAA;AAClB,OAAA;AACF,KAAC,CAAC,CAAA;GACH,CAAC,OAAOC,CAAC,EAAE;AACV;AACA;AACA;IACApC,OAAO,CAACC,IAAI,CACV,sBAAsB,GAAG+B,UAAU,GAAG,wBACxC,CAAC,CAAA;AACD,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;AACF,CAAC,CAAA;AAED,SAASK,eAAeA,GAAuB;AAAA,EAAA,IAAtBhB,MAAM,GAAA5C,SAAA,CAAAC,MAAA,GAAAD,CAAAA,IAAAA,SAAA,CAAA6D,CAAAA,CAAAA,KAAAA,SAAA,GAAA7D,SAAA,CAAG2C,CAAAA,CAAAA,GAAAA,SAAS,EAAE,CAAA;AAC3C,EAAA,IAAMmB,SAAS,GAAG,SAAZA,SAASA,CAAIC,IAAI,EAAA;IAAA,OAAKH,eAAe,CAACG,IAAI,CAAC,CAAA;AAAA,GAAA,CAAA;;AAEjD;AACF;AACA;AACA;EACED,SAAS,CAACE,OAAO,GAAGC,OAAO,CAAA;;AAE3B;AACF;AACA;AACA;EACEH,SAAS,CAACI,OAAO,GAAG,EAAE,CAAA;AAEtB,EAAA,IAAI,CAACtB,MAAM,IAAI,CAACA,MAAM,CAACG,QAAQ,IAAIH,MAAM,CAACG,QAAQ,CAACoB,QAAQ,KAAK,CAAC,EAAE;AACjE;AACA;IACAL,SAAS,CAACM,WAAW,GAAG,KAAK,CAAA;AAE7B,IAAA,OAAON,SAAS,CAAA;AAClB,GAAA;AAEA,EAAA,IAAMO,gBAAgB,GAAGzB,MAAM,CAACG,QAAQ,CAAA;AAExC,EAAA,IAAMA,QAAQ,GAAKH,MAAM,CAAnBG,QAAQ,CAAA;AACd,EAAA,IACEuB,gBAAgB,GASd1B,MAAM,CATR0B,gBAAgB;IAChBC,mBAAmB,GAQjB3B,MAAM,CARR2B,mBAAmB;IACnBC,IAAI,GAOF5B,MAAM,CAPR4B,IAAI;IACJC,OAAO,GAML7B,MAAM,CANR6B,OAAO;IACPC,UAAU,GAKR9B,MAAM,CALR8B,UAAU;IAAAC,oBAAA,GAKR/B,MAAM,CAJRgC,YAAY;IAAZA,YAAY,GAAAD,oBAAA,KAAA,KAAA,CAAA,GAAG/B,MAAM,CAACgC,YAAY,IAAIhC,MAAM,CAACiC,eAAe,GAAAF,oBAAA;IAC5DG,eAAe,GAGblC,MAAM,CAHRkC,eAAe;IACfC,SAAS,GAEPnC,MAAM,CAFRmC,SAAS;IACTjC,YAAY,GACVF,MAAM,CADRE,YAAY,CAAA;AAGd,EAAA,IAAMkC,gBAAgB,GAAGP,OAAO,CAACrG,SAAS,CAAA;AAE1C,EAAA,IAAM6G,SAAS,GAAGhE,YAAY,CAAC+D,gBAAgB,EAAE,WAAW,CAAC,CAAA;AAC7D,EAAA,IAAME,cAAc,GAAGjE,YAAY,CAAC+D,gBAAgB,EAAE,aAAa,CAAC,CAAA;AACpE,EAAA,IAAMG,aAAa,GAAGlE,YAAY,CAAC+D,gBAAgB,EAAE,YAAY,CAAC,CAAA;AAClE,EAAA,IAAMI,aAAa,GAAGnE,YAAY,CAAC+D,gBAAgB,EAAE,YAAY,CAAC,CAAA;;AAElE;AACA;AACA;AACA;AACA;AACA;AACA,EAAA,IAAI,OAAOT,mBAAmB,KAAK,UAAU,EAAE;AAC7C,IAAA,IAAMc,QAAQ,GAAGtC,QAAQ,CAACuC,aAAa,CAAC,UAAU,CAAC,CAAA;IACnD,IAAID,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACE,OAAO,CAACC,aAAa,EAAE;AACtDzC,MAAAA,QAAQ,GAAGsC,QAAQ,CAACE,OAAO,CAACC,aAAa,CAAA;AAC3C,KAAA;AACF,GAAA;AAEA,EAAA,IAAMC,kBAAkB,GAAG5C,yBAAyB,CAClDC,YAAY,EACZuB,gBACF,CAAC,CAAA;EACD,IAAMqB,SAAS,GAAGD,kBAAkB,GAAGA,kBAAkB,CAACjC,UAAU,CAAC,EAAE,CAAC,GAAG,EAAE,CAAA;EAE7E,IAAAmC,SAAA,GAKI5C,QAAQ;IAJV6C,cAAc,GAAAD,SAAA,CAAdC,cAAc;IACdC,kBAAkB,GAAAF,SAAA,CAAlBE,kBAAkB;IAClBC,sBAAsB,GAAAH,SAAA,CAAtBG,sBAAsB;IACtBC,oBAAoB,GAAAJ,SAAA,CAApBI,oBAAoB,CAAA;AAEtB,EAAA,IAAQC,UAAU,GAAK3B,gBAAgB,CAA/B2B,UAAU,CAAA;EAElB,IAAIC,YAAY,GAAG,EAAE,CAAA;EACrB,IAAI;AACFA,IAAAA,YAAY,GAAGpF,KAAK,CAACkC,QAAQ,CAAC,CAACkD,YAAY,GAAGlD,QAAQ,CAACkD,YAAY,GAAG,EAAE,CAAA;AAC1E,GAAC,CAAC,OAAOtC,CAAC,EAAE,EAAC;EAEb,IAAIuC,KAAK,GAAG,EAAE,CAAA;;AAEd;AACF;AACA;AACEpC,EAAAA,SAAS,CAACM,WAAW,GACnB,OAAOgB,aAAa,KAAK,UAAU,IACnCQ,cAAc,IACdA,cAAc,CAACO,kBAAkB,KAAKtC,SAAS,IAC/CoC,YAAY,KAAK,CAAC,CAAA;AAEpB,EAAA,IACEhE,eAAa,GAQXmE,aARW;IACblE,UAAQ,GAONkE,QAPM;IACRjE,aAAW,GAMTiE,WANS;IACXhE,WAAS,GAKPgE,SALO;IACT/D,WAAS,GAIP+D,SAJO;IACT7D,mBAAiB,GAGf6D,iBAHe;IACjB5D,iBAAe,GAEb4D,eAFa;IACf1D,gBAAc,GACZ0D,cADY,CAAA;AAGhB,EAAA,IAAM9D,gBAAc,GAAK8D,cAAL,CAAA;;AAEpB;AACF;AACA;AACA;;AAEE;EACA,IAAIC,YAAY,GAAG,IAAI,CAAA;AACvB,EAAA,IAAMC,oBAAoB,GAAGjG,QAAQ,CAAC,EAAE,EAAAkG,EAAAA,CAAAA,MAAA,CAAAvI,kBAAA,CACnCwI,MAAS,CAAAxI,EAAAA,kBAAA,CACTwI,KAAQ,CAAA,EAAAxI,kBAAA,CACRwI,UAAe,CAAA,EAAAxI,kBAAA,CACfwI,QAAW,GAAAxI,kBAAA,CACXwI,IAAS,EACb,CAAC,CAAA;;AAEF;EACA,IAAIC,YAAY,GAAG,IAAI,CAAA;AACvB,EAAA,IAAMC,oBAAoB,GAAGrG,QAAQ,CAAC,EAAE,EAAA,EAAA,CAAAkG,MAAA,CAAAvI,kBAAA,CACnC2I,IAAU,CAAA3I,EAAAA,kBAAA,CACV2I,GAAS,CAAA3I,EAAAA,kBAAA,CACT2I,MAAY,CAAA,EAAA3I,kBAAA,CACZ2I,GAAS,EACb,CAAC,CAAA;;AAEF;AACF;AACA;AACA;AACA;AACA;EACE,IAAIC,uBAAuB,GAAG9J,MAAM,CAACM,IAAI,CACvCN,MAAM,CAACO,MAAM,CAAC,IAAI,EAAE;AAClBwJ,IAAAA,YAAY,EAAE;AACZC,MAAAA,QAAQ,EAAE,IAAI;AACdC,MAAAA,YAAY,EAAE,KAAK;AACnBC,MAAAA,UAAU,EAAE,IAAI;AAChB3F,MAAAA,KAAK,EAAE,IAAA;KACR;AACD4F,IAAAA,kBAAkB,EAAE;AAClBH,MAAAA,QAAQ,EAAE,IAAI;AACdC,MAAAA,YAAY,EAAE,KAAK;AACnBC,MAAAA,UAAU,EAAE,IAAI;AAChB3F,MAAAA,KAAK,EAAE,IAAA;KACR;AACD6F,IAAAA,8BAA8B,EAAE;AAC9BJ,MAAAA,QAAQ,EAAE,IAAI;AACdC,MAAAA,YAAY,EAAE,KAAK;AACnBC,MAAAA,UAAU,EAAE,IAAI;AAChB3F,MAAAA,KAAK,EAAE,KAAA;AACT,KAAA;AACF,GAAC,CACH,CAAC,CAAA;;AAED;EACA,IAAI8F,WAAW,GAAG,IAAI,CAAA;;AAEtB;EACA,IAAIC,WAAW,GAAG,IAAI,CAAA;;AAEtB;EACA,IAAIC,eAAe,GAAG,IAAI,CAAA;;AAE1B;EACA,IAAIC,eAAe,GAAG,IAAI,CAAA;;AAE1B;EACA,IAAIC,uBAAuB,GAAG,KAAK,CAAA;;AAEnC;AACF;EACE,IAAIC,wBAAwB,GAAG,IAAI,CAAA;;AAEnC;AACF;AACA;EACE,IAAIC,kBAAkB,GAAG,KAAK,CAAA;;AAE9B;AACF;AACA;EACE,IAAIC,YAAY,GAAG,IAAI,CAAA;;AAEvB;EACA,IAAIC,cAAc,GAAG,KAAK,CAAA;;AAE1B;EACA,IAAIC,UAAU,GAAG,KAAK,CAAA;;AAEtB;AACF;EACE,IAAIC,UAAU,GAAG,KAAK,CAAA;;AAEtB;AACF;AACA;AACA;EACE,IAAIC,UAAU,GAAG,KAAK,CAAA;;AAEtB;AACF;EACE,IAAIC,mBAAmB,GAAG,KAAK,CAAA;;AAE/B;AACF;EACE,IAAIC,mBAAmB,GAAG,KAAK,CAAA;;AAE/B;AACF;AACA;EACE,IAAIC,YAAY,GAAG,IAAI,CAAA;;AAEvB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,IAAIC,oBAAoB,GAAG,KAAK,CAAA;EAChC,IAAMC,2BAA2B,GAAG,eAAe,CAAA;;AAEnD;EACA,IAAIC,YAAY,GAAG,IAAI,CAAA;;AAEvB;AACF;EACE,IAAIC,QAAQ,GAAG,KAAK,CAAA;;AAEpB;EACA,IAAIC,YAAY,GAAG,EAAE,CAAA;;AAErB;EACA,IAAIC,eAAe,GAAG,IAAI,CAAA;EAC1B,IAAMC,uBAAuB,GAAGnI,QAAQ,CAAC,EAAE,EAAE,CAC3C,gBAAgB,EAChB,OAAO,EACP,UAAU,EACV,MAAM,EACN,eAAe,EACf,MAAM,EACN,QAAQ,EACR,MAAM,EACN,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,OAAO,EACP,SAAS,EACT,UAAU,EACV,UAAU,EACV,WAAW,EACX,QAAQ,EACR,OAAO,EACP,KAAK,EACL,UAAU,EACV,OAAO,EACP,OAAO,EACP,OAAO,EACP,KAAK,CACN,CAAC,CAAA;;AAEF;EACA,IAAIoI,aAAa,GAAG,IAAI,CAAA;EACxB,IAAMC,qBAAqB,GAAGrI,QAAQ,CAAC,EAAE,EAAE,CACzC,OAAO,EACP,OAAO,EACP,KAAK,EACL,QAAQ,EACR,OAAO,EACP,OAAO,CACR,CAAC,CAAA;;AAEF;EACA,IAAIsI,mBAAmB,GAAG,IAAI,CAAA;AAC9B,EAAA,IAAMC,2BAA2B,GAAGvI,QAAQ,CAAC,EAAE,EAAE,CAC/C,KAAK,EACL,OAAO,EACP,KAAK,EACL,IAAI,EACJ,OAAO,EACP,MAAM,EACN,SAAS,EACT,aAAa,EACb,MAAM,EACN,SAAS,EACT,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,CACR,CAAC,CAAA;EAEF,IAAMwI,gBAAgB,GAAG,oCAAoC,CAAA;EAC7D,IAAMC,aAAa,GAAG,4BAA4B,CAAA;EAClD,IAAMC,cAAc,GAAG,8BAA8B,CAAA;AACrD;EACA,IAAIC,SAAS,GAAGD,cAAc,CAAA;EAC9B,IAAIE,cAAc,GAAG,KAAK,CAAA;;AAE1B;EACA,IAAIC,kBAAkB,GAAG,IAAI,CAAA;AAC7B,EAAA,IAAMC,0BAA0B,GAAG9I,QAAQ,CACzC,EAAE,EACF,CAACwI,gBAAgB,EAAEC,aAAa,EAAEC,cAAc,CAAC,EACjDlK,cACF,CAAC,CAAA;;AAED;AACA,EAAA,IAAIuK,iBAAiB,CAAA;AACrB,EAAA,IAAMC,4BAA4B,GAAG,CAAC,uBAAuB,EAAE,WAAW,CAAC,CAAA;EAC3E,IAAMC,yBAAyB,GAAG,WAAW,CAAA;AAC7C,EAAA,IAAI9I,iBAAiB,CAAA;;AAErB;EACA,IAAI+I,MAAM,GAAG,IAAI,CAAA;;AAEjB;AACA;;AAEA,EAAA,IAAMC,WAAW,GAAGzG,QAAQ,CAACuC,aAAa,CAAC,MAAM,CAAC,CAAA;AAElD,EAAA,IAAMmE,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAaC,SAAS,EAAE;AAC7C,IAAA,OAAOA,SAAS,YAAYlK,MAAM,IAAIkK,SAAS,YAAYC,QAAQ,CAAA;GACpE,CAAA;;AAED;AACF;AACA;AACA;AACA;AACE;AACA,EAAA,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAaC,GAAG,EAAE;AAClC,IAAA,IAAIN,MAAM,IAAIA,MAAM,KAAKM,GAAG,EAAE;AAC5B,MAAA,OAAA;AACF,KAAA;;AAEA;IACA,IAAI,CAACA,GAAG,IAAI7G,OAAA,CAAO6G,GAAG,CAAA,KAAK,QAAQ,EAAE;MACnCA,GAAG,GAAG,EAAE,CAAA;AACV,KAAA;;AAEA;AACAA,IAAAA,GAAG,GAAGhJ,KAAK,CAACgJ,GAAG,CAAC,CAAA;IAEhBT,iBAAiB;AACf;AACAC,IAAAA,4BAA4B,CAACjK,OAAO,CAACyK,GAAG,CAACT,iBAAiB,CAAC,KAAK,CAAC,CAAC,GAC7DA,iBAAiB,GAAGE,yBAAyB,GAC7CF,iBAAiB,GAAGS,GAAG,CAACT,iBAAkB,CAAA;;AAEjD;AACA5I,IAAAA,iBAAiB,GACf4I,iBAAiB,KAAK,uBAAuB,GACzCvK,cAAc,GACdH,iBAAiB,CAAA;;AAEvB;AACA2H,IAAAA,YAAY,GACV,cAAc,IAAIwD,GAAG,GACjBxJ,QAAQ,CAAC,EAAE,EAAEwJ,GAAG,CAACxD,YAAY,EAAE7F,iBAAiB,CAAC,GACjD8F,oBAAoB,CAAA;AAC1BG,IAAAA,YAAY,GACV,cAAc,IAAIoD,GAAG,GACjBxJ,QAAQ,CAAC,EAAE,EAAEwJ,GAAG,CAACpD,YAAY,EAAEjG,iBAAiB,CAAC,GACjDkG,oBAAoB,CAAA;AAC1BwC,IAAAA,kBAAkB,GAChB,oBAAoB,IAAIW,GAAG,GACvBxJ,QAAQ,CAAC,EAAE,EAAEwJ,GAAG,CAACX,kBAAkB,EAAErK,cAAc,CAAC,GACpDsK,0BAA0B,CAAA;IAChCR,mBAAmB,GACjB,mBAAmB,IAAIkB,GAAG,GACtBxJ,QAAQ,CACNQ,KAAK,CAAC+H,2BAA2B,CAAC;AAAE;AACpCiB,IAAAA,GAAG,CAACC,iBAAiB;AAAE;AACvBtJ,IAAAA,iBAAiB;AACnB,KAAC;AAAC,MACFoI,2BAA2B,CAAA;IACjCH,aAAa,GACX,mBAAmB,IAAIoB,GAAG,GACtBxJ,QAAQ,CACNQ,KAAK,CAAC6H,qBAAqB,CAAC;AAAE;AAC9BmB,IAAAA,GAAG,CAACE,iBAAiB;AAAE;AACvBvJ,IAAAA,iBAAiB;AACnB,KAAC;AAAC,MACFkI,qBAAqB,CAAA;AAC3BH,IAAAA,eAAe,GACb,iBAAiB,IAAIsB,GAAG,GACpBxJ,QAAQ,CAAC,EAAE,EAAEwJ,GAAG,CAACtB,eAAe,EAAE/H,iBAAiB,CAAC,GACpDgI,uBAAuB,CAAA;AAC7BrB,IAAAA,WAAW,GACT,aAAa,IAAI0C,GAAG,GAChBxJ,QAAQ,CAAC,EAAE,EAAEwJ,GAAG,CAAC1C,WAAW,EAAE3G,iBAAiB,CAAC,GAChD,EAAE,CAAA;AACR4G,IAAAA,WAAW,GACT,aAAa,IAAIyC,GAAG,GAChBxJ,QAAQ,CAAC,EAAE,EAAEwJ,GAAG,CAACzC,WAAW,EAAE5G,iBAAiB,CAAC,GAChD,EAAE,CAAA;IACR8H,YAAY,GAAG,cAAc,IAAIuB,GAAG,GAAGA,GAAG,CAACvB,YAAY,GAAG,KAAK,CAAA;AAC/DjB,IAAAA,eAAe,GAAGwC,GAAG,CAACxC,eAAe,KAAK,KAAK,CAAC;AAChDC,IAAAA,eAAe,GAAGuC,GAAG,CAACvC,eAAe,KAAK,KAAK,CAAC;AAChDC,IAAAA,uBAAuB,GAAGsC,GAAG,CAACtC,uBAAuB,IAAI,KAAK,CAAC;AAC/DC,IAAAA,wBAAwB,GAAGqC,GAAG,CAACrC,wBAAwB,KAAK,KAAK,CAAC;AAClEC,IAAAA,kBAAkB,GAAGoC,GAAG,CAACpC,kBAAkB,IAAI,KAAK,CAAC;AACrDC,IAAAA,YAAY,GAAGmC,GAAG,CAACnC,YAAY,KAAK,KAAK,CAAC;AAC1CC,IAAAA,cAAc,GAAGkC,GAAG,CAAClC,cAAc,IAAI,KAAK,CAAC;AAC7CG,IAAAA,UAAU,GAAG+B,GAAG,CAAC/B,UAAU,IAAI,KAAK,CAAC;AACrCC,IAAAA,mBAAmB,GAAG8B,GAAG,CAAC9B,mBAAmB,IAAI,KAAK,CAAC;AACvDC,IAAAA,mBAAmB,GAAG6B,GAAG,CAAC7B,mBAAmB,IAAI,KAAK,CAAC;AACvDH,IAAAA,UAAU,GAAGgC,GAAG,CAAChC,UAAU,IAAI,KAAK,CAAC;AACrCI,IAAAA,YAAY,GAAG4B,GAAG,CAAC5B,YAAY,KAAK,KAAK,CAAC;AAC1CC,IAAAA,oBAAoB,GAAG2B,GAAG,CAAC3B,oBAAoB,IAAI,KAAK,CAAC;AACzDE,IAAAA,YAAY,GAAGyB,GAAG,CAACzB,YAAY,KAAK,KAAK,CAAC;AAC1CC,IAAAA,QAAQ,GAAGwB,GAAG,CAACxB,QAAQ,IAAI,KAAK,CAAC;AACjC/F,IAAAA,gBAAc,GAAGuH,GAAG,CAACG,kBAAkB,IAAI1H,gBAAc,CAAA;AACzD0G,IAAAA,SAAS,GAAGa,GAAG,CAACb,SAAS,IAAID,cAAc,CAAA;AAC3CnC,IAAAA,uBAAuB,GAAGiD,GAAG,CAACjD,uBAAuB,IAAI,EAAE,CAAA;AAC3D,IAAA,IACEiD,GAAG,CAACjD,uBAAuB,IAC3B6C,iBAAiB,CAACI,GAAG,CAACjD,uBAAuB,CAACC,YAAY,CAAC,EAC3D;AACAD,MAAAA,uBAAuB,CAACC,YAAY,GAClCgD,GAAG,CAACjD,uBAAuB,CAACC,YAAY,CAAA;AAC5C,KAAA;AAEA,IAAA,IACEgD,GAAG,CAACjD,uBAAuB,IAC3B6C,iBAAiB,CAACI,GAAG,CAACjD,uBAAuB,CAACK,kBAAkB,CAAC,EACjE;AACAL,MAAAA,uBAAuB,CAACK,kBAAkB,GACxC4C,GAAG,CAACjD,uBAAuB,CAACK,kBAAkB,CAAA;AAClD,KAAA;AAEA,IAAA,IACE4C,GAAG,CAACjD,uBAAuB,IAC3B,OAAOiD,GAAG,CAACjD,uBAAuB,CAACM,8BAA8B,KAC/D,SAAS,EACX;AACAN,MAAAA,uBAAuB,CAACM,8BAA8B,GACpD2C,GAAG,CAACjD,uBAAuB,CAACM,8BAA8B,CAAA;AAC9D,KAAA;AAEA,IAAA,IAAIO,kBAAkB,EAAE;AACtBH,MAAAA,eAAe,GAAG,KAAK,CAAA;AACzB,KAAA;AAEA,IAAA,IAAIS,mBAAmB,EAAE;AACvBD,MAAAA,UAAU,GAAG,IAAI,CAAA;AACnB,KAAA;;AAEA;AACA,IAAA,IAAIQ,YAAY,EAAE;AAChBjC,MAAAA,YAAY,GAAGhG,QAAQ,CAAC,EAAE,EAAArC,kBAAA,CAAMwI,IAAS,CAAC,CAAC,CAAA;AAC3CC,MAAAA,YAAY,GAAG,EAAE,CAAA;AACjB,MAAA,IAAI6B,YAAY,CAAC7G,IAAI,KAAK,IAAI,EAAE;AAC9BpB,QAAAA,QAAQ,CAACgG,YAAY,EAAEG,MAAS,CAAC,CAAA;AACjCnG,QAAAA,QAAQ,CAACoG,YAAY,EAAEE,IAAU,CAAC,CAAA;AACpC,OAAA;AAEA,MAAA,IAAI2B,YAAY,CAAC5G,GAAG,KAAK,IAAI,EAAE;AAC7BrB,QAAAA,QAAQ,CAACgG,YAAY,EAAEG,KAAQ,CAAC,CAAA;AAChCnG,QAAAA,QAAQ,CAACoG,YAAY,EAAEE,GAAS,CAAC,CAAA;AACjCtG,QAAAA,QAAQ,CAACoG,YAAY,EAAEE,GAAS,CAAC,CAAA;AACnC,OAAA;AAEA,MAAA,IAAI2B,YAAY,CAAC3G,UAAU,KAAK,IAAI,EAAE;AACpCtB,QAAAA,QAAQ,CAACgG,YAAY,EAAEG,UAAe,CAAC,CAAA;AACvCnG,QAAAA,QAAQ,CAACoG,YAAY,EAAEE,GAAS,CAAC,CAAA;AACjCtG,QAAAA,QAAQ,CAACoG,YAAY,EAAEE,GAAS,CAAC,CAAA;AACnC,OAAA;AAEA,MAAA,IAAI2B,YAAY,CAACzG,MAAM,KAAK,IAAI,EAAE;AAChCxB,QAAAA,QAAQ,CAACgG,YAAY,EAAEG,QAAW,CAAC,CAAA;AACnCnG,QAAAA,QAAQ,CAACoG,YAAY,EAAEE,MAAY,CAAC,CAAA;AACpCtG,QAAAA,QAAQ,CAACoG,YAAY,EAAEE,GAAS,CAAC,CAAA;AACnC,OAAA;AACF,KAAA;;AAEA;IACA,IAAIkD,GAAG,CAACI,QAAQ,EAAE;MAChB,IAAI5D,YAAY,KAAKC,oBAAoB,EAAE;AACzCD,QAAAA,YAAY,GAAGxF,KAAK,CAACwF,YAAY,CAAC,CAAA;AACpC,OAAA;MAEAhG,QAAQ,CAACgG,YAAY,EAAEwD,GAAG,CAACI,QAAQ,EAAEzJ,iBAAiB,CAAC,CAAA;AACzD,KAAA;IAEA,IAAIqJ,GAAG,CAACK,QAAQ,EAAE;MAChB,IAAIzD,YAAY,KAAKC,oBAAoB,EAAE;AACzCD,QAAAA,YAAY,GAAG5F,KAAK,CAAC4F,YAAY,CAAC,CAAA;AACpC,OAAA;MAEApG,QAAQ,CAACoG,YAAY,EAAEoD,GAAG,CAACK,QAAQ,EAAE1J,iBAAiB,CAAC,CAAA;AACzD,KAAA;IAEA,IAAIqJ,GAAG,CAACC,iBAAiB,EAAE;MACzBzJ,QAAQ,CAACsI,mBAAmB,EAAEkB,GAAG,CAACC,iBAAiB,EAAEtJ,iBAAiB,CAAC,CAAA;AACzE,KAAA;IAEA,IAAIqJ,GAAG,CAACtB,eAAe,EAAE;MACvB,IAAIA,eAAe,KAAKC,uBAAuB,EAAE;AAC/CD,QAAAA,eAAe,GAAG1H,KAAK,CAAC0H,eAAe,CAAC,CAAA;AAC1C,OAAA;MAEAlI,QAAQ,CAACkI,eAAe,EAAEsB,GAAG,CAACtB,eAAe,EAAE/H,iBAAiB,CAAC,CAAA;AACnE,KAAA;;AAEA;AACA,IAAA,IAAI4H,YAAY,EAAE;AAChB/B,MAAAA,YAAY,CAAC,OAAO,CAAC,GAAG,IAAI,CAAA;AAC9B,KAAA;;AAEA;AACA,IAAA,IAAIsB,cAAc,EAAE;MAClBtH,QAAQ,CAACgG,YAAY,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAA;AAClD,KAAA;;AAEA;IACA,IAAIA,YAAY,CAAC8D,KAAK,EAAE;AACtB9J,MAAAA,QAAQ,CAACgG,YAAY,EAAE,CAAC,OAAO,CAAC,CAAC,CAAA;MACjC,OAAOc,WAAW,CAACiD,KAAK,CAAA;AAC1B,KAAA;;AAEA;AACA;AACA,IAAA,IAAIjN,MAAM,EAAE;MACVA,MAAM,CAAC0M,GAAG,CAAC,CAAA;AACb,KAAA;AAEAN,IAAAA,MAAM,GAAGM,GAAG,CAAA;GACb,CAAA;AAED,EAAA,IAAMQ,8BAA8B,GAAGhK,QAAQ,CAAC,EAAE,EAAE,CAClD,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,OAAO,CACR,CAAC,CAAA;EAEF,IAAMiK,uBAAuB,GAAGjK,QAAQ,CAAC,EAAE,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAA;;AAEhE;AACA;AACA;AACA;AACA,EAAA,IAAMkK,4BAA4B,GAAGlK,QAAQ,CAAC,EAAE,EAAE,CAChD,OAAO,EACP,OAAO,EACP,MAAM,EACN,GAAG,EACH,QAAQ,CACT,CAAC,CAAA;;AAEF;AACF;AACA;EACE,IAAMmK,YAAY,GAAGnK,QAAQ,CAAC,EAAE,EAAEmG,KAAQ,CAAC,CAAA;AAC3CnG,EAAAA,QAAQ,CAACmK,YAAY,EAAEhE,UAAe,CAAC,CAAA;AACvCnG,EAAAA,QAAQ,CAACmK,YAAY,EAAEhE,aAAkB,CAAC,CAAA;EAE1C,IAAMiE,eAAe,GAAGpK,QAAQ,CAAC,EAAE,EAAEmG,QAAW,CAAC,CAAA;AACjDnG,EAAAA,QAAQ,CAACoK,eAAe,EAAEjE,gBAAqB,CAAC,CAAA;;AAEhD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACE,EAAA,IAAMkE,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAa/J,OAAO,EAAE;AAC9C,IAAA,IAAIgK,MAAM,GAAGvF,aAAa,CAACzE,OAAO,CAAC,CAAA;;AAEnC;AACA;AACA,IAAA,IAAI,CAACgK,MAAM,IAAI,CAACA,MAAM,CAACC,OAAO,EAAE;AAC9BD,MAAAA,MAAM,GAAG;AACPE,QAAAA,YAAY,EAAE7B,SAAS;AACvB4B,QAAAA,OAAO,EAAE,UAAA;OACV,CAAA;AACH,KAAA;AAEA,IAAA,IAAMA,OAAO,GAAGlM,iBAAiB,CAACiC,OAAO,CAACiK,OAAO,CAAC,CAAA;AAClD,IAAA,IAAME,aAAa,GAAGpM,iBAAiB,CAACiM,MAAM,CAACC,OAAO,CAAC,CAAA;AAEvD,IAAA,IAAI,CAAC1B,kBAAkB,CAACvI,OAAO,CAACkK,YAAY,CAAC,EAAE;AAC7C,MAAA,OAAO,KAAK,CAAA;AACd,KAAA;AAEA,IAAA,IAAIlK,OAAO,CAACkK,YAAY,KAAK/B,aAAa,EAAE;AAC1C;AACA;AACA;AACA,MAAA,IAAI6B,MAAM,CAACE,YAAY,KAAK9B,cAAc,EAAE;QAC1C,OAAO6B,OAAO,KAAK,KAAK,CAAA;AAC1B,OAAA;;AAEA;AACA;AACA;AACA,MAAA,IAAID,MAAM,CAACE,YAAY,KAAKhC,gBAAgB,EAAE;AAC5C,QAAA,OACE+B,OAAO,KAAK,KAAK,KAChBE,aAAa,KAAK,gBAAgB,IACjCT,8BAA8B,CAACS,aAAa,CAAC,CAAC,CAAA;AAEpD,OAAA;;AAEA;AACA;AACA,MAAA,OAAOC,OAAO,CAACP,YAAY,CAACI,OAAO,CAAC,CAAC,CAAA;AACvC,KAAA;AAEA,IAAA,IAAIjK,OAAO,CAACkK,YAAY,KAAKhC,gBAAgB,EAAE;AAC7C;AACA;AACA;AACA,MAAA,IAAI8B,MAAM,CAACE,YAAY,KAAK9B,cAAc,EAAE;QAC1C,OAAO6B,OAAO,KAAK,MAAM,CAAA;AAC3B,OAAA;;AAEA;AACA;AACA,MAAA,IAAID,MAAM,CAACE,YAAY,KAAK/B,aAAa,EAAE;AACzC,QAAA,OAAO8B,OAAO,KAAK,MAAM,IAAIN,uBAAuB,CAACQ,aAAa,CAAC,CAAA;AACrE,OAAA;;AAEA;AACA;AACA,MAAA,OAAOC,OAAO,CAACN,eAAe,CAACG,OAAO,CAAC,CAAC,CAAA;AAC1C,KAAA;AAEA,IAAA,IAAIjK,OAAO,CAACkK,YAAY,KAAK9B,cAAc,EAAE;AAC3C;AACA;AACA;MACA,IACE4B,MAAM,CAACE,YAAY,KAAK/B,aAAa,IACrC,CAACwB,uBAAuB,CAACQ,aAAa,CAAC,EACvC;AACA,QAAA,OAAO,KAAK,CAAA;AACd,OAAA;MAEA,IACEH,MAAM,CAACE,YAAY,KAAKhC,gBAAgB,IACxC,CAACwB,8BAA8B,CAACS,aAAa,CAAC,EAC9C;AACA,QAAA,OAAO,KAAK,CAAA;AACd,OAAA;;AAEA;AACA;AACA,MAAA,OACE,CAACL,eAAe,CAACG,OAAO,CAAC,KACxBL,4BAA4B,CAACK,OAAO,CAAC,IAAI,CAACJ,YAAY,CAACI,OAAO,CAAC,CAAC,CAAA;AAErE,KAAA;;AAEA;IACA,IACExB,iBAAiB,KAAK,uBAAuB,IAC7CF,kBAAkB,CAACvI,OAAO,CAACkK,YAAY,CAAC,EACxC;AACA,MAAA,OAAO,IAAI,CAAA;AACb,KAAA;;AAEA;AACA;AACA;AACA;AACA,IAAA,OAAO,KAAK,CAAA;GACb,CAAA;;AAED;AACF;AACA;AACA;AACA;AACE,EAAA,IAAMG,YAAY,GAAG,SAAfA,YAAYA,CAAaC,IAAI,EAAE;AACnCzM,IAAAA,SAAS,CAACsF,SAAS,CAACI,OAAO,EAAE;AAAEvD,MAAAA,OAAO,EAAEsK,IAAAA;AAAK,KAAC,CAAC,CAAA;IAC/C,IAAI;AACF;AACAA,MAAAA,IAAI,CAACC,UAAU,CAACC,WAAW,CAACF,IAAI,CAAC,CAAA;KAClC,CAAC,OAAOtH,CAAC,EAAE;MACV,IAAI;QACFsH,IAAI,CAACG,SAAS,GAAG1F,SAAS,CAAA;OAC3B,CAAC,OAAO/B,CAAC,EAAE;QACVsH,IAAI,CAACI,MAAM,EAAE,CAAA;AACf,OAAA;AACF,KAAA;GACD,CAAA;;AAED;AACF;AACA;AACA;AACA;AACA;EACE,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAaC,IAAI,EAAEN,IAAI,EAAE;IAC7C,IAAI;AACFzM,MAAAA,SAAS,CAACsF,SAAS,CAACI,OAAO,EAAE;AAC3BsH,QAAAA,SAAS,EAAEP,IAAI,CAACQ,gBAAgB,CAACF,IAAI,CAAC;AACtCG,QAAAA,IAAI,EAAET,IAAAA;AACR,OAAC,CAAC,CAAA;KACH,CAAC,OAAOtH,CAAC,EAAE;AACVnF,MAAAA,SAAS,CAACsF,SAAS,CAACI,OAAO,EAAE;AAC3BsH,QAAAA,SAAS,EAAE,IAAI;AACfE,QAAAA,IAAI,EAAET,IAAAA;AACR,OAAC,CAAC,CAAA;AACJ,KAAA;AAEAA,IAAAA,IAAI,CAACU,eAAe,CAACJ,IAAI,CAAC,CAAA;;AAE1B;IACA,IAAIA,IAAI,KAAK,IAAI,IAAI,CAAC9E,YAAY,CAAC8E,IAAI,CAAC,EAAE;MACxC,IAAIzD,UAAU,IAAIC,mBAAmB,EAAE;QACrC,IAAI;UACFiD,YAAY,CAACC,IAAI,CAAC,CAAA;AACpB,SAAC,CAAC,OAAOtH,CAAC,EAAE,EAAC;AACf,OAAC,MAAM;QACL,IAAI;AACFsH,UAAAA,IAAI,CAACW,YAAY,CAACL,IAAI,EAAE,EAAE,CAAC,CAAA;AAC7B,SAAC,CAAC,OAAO5H,CAAC,EAAE,EAAC;AACf,OAAA;AACF,KAAA;GACD,CAAA;;AAED;AACF;AACA;AACA;AACA;AACA;AACE,EAAA,IAAMkI,aAAa,GAAG,SAAhBA,aAAaA,CAAaC,KAAK,EAAE;AACrC;AACA,IAAA,IAAIC,GAAG,CAAA;AACP,IAAA,IAAIC,iBAAiB,CAAA;AAErB,IAAA,IAAInE,UAAU,EAAE;MACdiE,KAAK,GAAG,mBAAmB,GAAGA,KAAK,CAAA;AACrC,KAAC,MAAM;AACL;AACA,MAAA,IAAMG,OAAO,GAAGlN,WAAW,CAAC+M,KAAK,EAAE,aAAa,CAAC,CAAA;AACjDE,MAAAA,iBAAiB,GAAGC,OAAO,IAAIA,OAAO,CAAC,CAAC,CAAC,CAAA;AAC3C,KAAA;AAEA,IAAA,IACE7C,iBAAiB,KAAK,uBAAuB,IAC7CJ,SAAS,KAAKD,cAAc,EAC5B;AACA;AACA+C,MAAAA,KAAK,GACH,gEAAgE,GAChEA,KAAK,GACL,gBAAgB,CAAA;AACpB,KAAA;IAEA,IAAMI,YAAY,GAAGzG,kBAAkB,GACnCA,kBAAkB,CAACjC,UAAU,CAACsI,KAAK,CAAC,GACpCA,KAAK,CAAA;AACT;AACJ;AACA;AACA;IACI,IAAI9C,SAAS,KAAKD,cAAc,EAAE;MAChC,IAAI;QACFgD,GAAG,GAAG,IAAIhH,SAAS,EAAE,CAACoH,eAAe,CAACD,YAAY,EAAE9C,iBAAiB,CAAC,CAAA;AACxE,OAAC,CAAC,OAAOzF,CAAC,EAAE,EAAC;AACf,KAAA;;AAEA;AACA,IAAA,IAAI,CAACoI,GAAG,IAAI,CAACA,GAAG,CAACK,eAAe,EAAE;MAChCL,GAAG,GAAGnG,cAAc,CAACyG,cAAc,CAACrD,SAAS,EAAE,UAAU,EAAE,IAAI,CAAC,CAAA;MAChE,IAAI;QACF+C,GAAG,CAACK,eAAe,CAACE,SAAS,GAAGrD,cAAc,GAC1CvD,SAAS,GACTwG,YAAY,CAAA;OACjB,CAAC,OAAOvI,CAAC,EAAE;AACV;AAAA,OAAA;AAEJ,KAAA;IAEA,IAAM4I,IAAI,GAAGR,GAAG,CAACQ,IAAI,IAAIR,GAAG,CAACK,eAAe,CAAA;IAE5C,IAAIN,KAAK,IAAIE,iBAAiB,EAAE;AAC9BO,MAAAA,IAAI,CAACC,YAAY,CACfzJ,QAAQ,CAAC0J,cAAc,CAACT,iBAAiB,CAAC,EAC1CO,IAAI,CAACG,UAAU,CAAC,CAAC,CAAC,IAAI,IACxB,CAAC,CAAA;AACH,KAAA;;AAEA;IACA,IAAI1D,SAAS,KAAKD,cAAc,EAAE;AAChC,MAAA,OAAOhD,oBAAoB,CAAC4G,IAAI,CAC9BZ,GAAG,EACHpE,cAAc,GAAG,MAAM,GAAG,MAC5B,CAAC,CAAC,CAAC,CAAC,CAAA;AACN,KAAA;AAEA,IAAA,OAAOA,cAAc,GAAGoE,GAAG,CAACK,eAAe,GAAGG,IAAI,CAAA;GACnD,CAAA;;AAED;AACF;AACA;AACA;AACA;AACA;AACE,EAAA,IAAMK,eAAe,GAAG,SAAlBA,eAAeA,CAAa7I,IAAI,EAAE;IACtC,OAAO8B,kBAAkB,CAAC8G,IAAI,CAC5B5I,IAAI,CAACyB,aAAa,IAAIzB,IAAI,EAC1BA,IAAI;AACJ;IACAW,UAAU,CAACmI,YAAY,GACrBnI,UAAU,CAACoI,YAAY,GACvBpI,UAAU,CAACqI,SAAS,GACpBrI,UAAU,CAACsI,2BAA2B,GACtCtI,UAAU,CAACuI,kBAAkB,EAC/B,IAAI,EACJ,KACF,CAAC,CAAA;GACF,CAAA;;AAED;AACF;AACA;AACA;AACA;AACA;AACE,EAAA,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAaC,GAAG,EAAE;AAClC,IAAA,OACEA,GAAG,YAAYrI,eAAe,KAC7B,OAAOqI,GAAG,CAACC,QAAQ,KAAK,QAAQ,IAC/B,OAAOD,GAAG,CAACE,WAAW,KAAK,QAAQ,IACnC,OAAOF,GAAG,CAAChC,WAAW,KAAK,UAAU,IACrC,EAAEgC,GAAG,CAACG,UAAU,YAAY1I,YAAY,CAAC,IACzC,OAAOuI,GAAG,CAACxB,eAAe,KAAK,UAAU,IACzC,OAAOwB,GAAG,CAACvB,YAAY,KAAK,UAAU,IACtC,OAAOuB,GAAG,CAACtC,YAAY,KAAK,QAAQ,IACpC,OAAOsC,GAAG,CAACX,YAAY,KAAK,UAAU,IACtC,OAAOW,GAAG,CAACI,aAAa,KAAK,UAAU,CAAC,CAAA;GAE7C,CAAA;;AAED;AACF;AACA;AACA;AACA;AACA;AACE,EAAA,IAAMC,OAAO,GAAG,SAAVA,OAAOA,CAAa1M,MAAM,EAAE;AAChC,IAAA,OAAOkC,OAAA,CAAOwB,IAAI,CAAA,KAAK,QAAQ,GAC3B1D,MAAM,YAAY0D,IAAI,GACtB1D,MAAM,IACJkC,OAAA,CAAOlC,MAAM,CAAK,KAAA,QAAQ,IAC1B,OAAOA,MAAM,CAACqD,QAAQ,KAAK,QAAQ,IACnC,OAAOrD,MAAM,CAACsM,QAAQ,KAAK,QAAQ,CAAA;GAC1C,CAAA;;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,IAAMK,YAAY,GAAG,SAAfA,YAAYA,CAAaC,UAAU,EAAEC,WAAW,EAAEC,IAAI,EAAE;AAC5D,IAAA,IAAI,CAAC1H,KAAK,CAACwH,UAAU,CAAC,EAAE;AACtB,MAAA,OAAA;AACF,KAAA;IAEAzP,YAAY,CAACiI,KAAK,CAACwH,UAAU,CAAC,EAAE,UAACG,IAAI,EAAK;MACxCA,IAAI,CAAClB,IAAI,CAAC7I,SAAS,EAAE6J,WAAW,EAAEC,IAAI,EAAErE,MAAM,CAAC,CAAA;AACjD,KAAC,CAAC,CAAA;GACH,CAAA;;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE,EAAA,IAAMuE,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAaH,WAAW,EAAE;AAC/C,IAAA,IAAIpI,OAAO,CAAA;;AAEX;AACAkI,IAAAA,YAAY,CAAC,wBAAwB,EAAEE,WAAW,EAAE,IAAI,CAAC,CAAA;;AAEzD;AACA,IAAA,IAAIT,YAAY,CAACS,WAAW,CAAC,EAAE;MAC7B3C,YAAY,CAAC2C,WAAW,CAAC,CAAA;AACzB,MAAA,OAAO,IAAI,CAAA;AACb,KAAA;;AAEA;IACA,IAAIpO,UAAU,CAAC,iBAAiB,EAAEoO,WAAW,CAACP,QAAQ,CAAC,EAAE;MACvDpC,YAAY,CAAC2C,WAAW,CAAC,CAAA;AACzB,MAAA,OAAO,IAAI,CAAA;AACb,KAAA;;AAEA;AACA,IAAA,IAAM/C,OAAO,GAAGpK,iBAAiB,CAACmN,WAAW,CAACP,QAAQ,CAAC,CAAA;;AAEvD;AACAK,IAAAA,YAAY,CAAC,qBAAqB,EAAEE,WAAW,EAAE;AAC/C/C,MAAAA,OAAO,EAAPA,OAAO;AACPmD,MAAAA,WAAW,EAAE1H,YAAAA;AACf,KAAC,CAAC,CAAA;;AAEF;IACA,IACEsH,WAAW,CAACJ,aAAa,EAAE,IAC3B,CAACC,OAAO,CAACG,WAAW,CAACK,iBAAiB,CAAC,KACtC,CAACR,OAAO,CAACG,WAAW,CAACpI,OAAO,CAAC,IAC5B,CAACiI,OAAO,CAACG,WAAW,CAACpI,OAAO,CAACyI,iBAAiB,CAAC,CAAC,IAClDzO,UAAU,CAAC,SAAS,EAAEoO,WAAW,CAACrB,SAAS,CAAC,IAC5C/M,UAAU,CAAC,SAAS,EAAEoO,WAAW,CAACN,WAAW,CAAC,EAC9C;MACArC,YAAY,CAAC2C,WAAW,CAAC,CAAA;AACzB,MAAA,OAAO,IAAI,CAAA;AACb,KAAA;;AAEA;AACA,IAAA,IACE/C,OAAO,KAAK,QAAQ,IACpBrL,UAAU,CAAC,YAAY,EAAEoO,WAAW,CAACrB,SAAS,CAAC,EAC/C;MACAtB,YAAY,CAAC2C,WAAW,CAAC,CAAA;AACzB,MAAA,OAAO,IAAI,CAAA;AACb,KAAA;;AAEA;AACA,IAAA,IAAIA,WAAW,CAACxJ,QAAQ,KAAK,CAAC,EAAE;MAC9B6G,YAAY,CAAC2C,WAAW,CAAC,CAAA;AACzB,MAAA,OAAO,IAAI,CAAA;AACb,KAAA;;AAEA;AACA,IAAA,IACEjG,YAAY,IACZiG,WAAW,CAACxJ,QAAQ,KAAK,CAAC,IAC1B5E,UAAU,CAAC,SAAS,EAAEoO,WAAW,CAACC,IAAI,CAAC,EACvC;MACA5C,YAAY,CAAC2C,WAAW,CAAC,CAAA;AACzB,MAAA,OAAO,IAAI,CAAA;AACb,KAAA;;AAEA;IACA,IAAI,CAACtH,YAAY,CAACuE,OAAO,CAAC,IAAIzD,WAAW,CAACyD,OAAO,CAAC,EAAE;AAClD;MACA,IAAI,CAACzD,WAAW,CAACyD,OAAO,CAAC,IAAIqD,uBAAuB,CAACrD,OAAO,CAAC,EAAE;AAC7D,QAAA,IACEhE,uBAAuB,CAACC,YAAY,YAAYrH,MAAM,IACtDD,UAAU,CAACqH,uBAAuB,CAACC,YAAY,EAAE+D,OAAO,CAAC,EAEzD,OAAO,KAAK,CAAA;AACd,QAAA,IACEhE,uBAAuB,CAACC,YAAY,YAAY8C,QAAQ,IACxD/C,uBAAuB,CAACC,YAAY,CAAC+D,OAAO,CAAC,EAE7C,OAAO,KAAK,CAAA;AAChB,OAAA;;AAEA;AACA,MAAA,IAAIxC,YAAY,IAAI,CAACG,eAAe,CAACqC,OAAO,CAAC,EAAE;QAC7C,IAAMM,UAAU,GAAG9F,aAAa,CAACuI,WAAW,CAAC,IAAIA,WAAW,CAACzC,UAAU,CAAA;QACvE,IAAMwB,UAAU,GAAGvH,aAAa,CAACwI,WAAW,CAAC,IAAIA,WAAW,CAACjB,UAAU,CAAA;QAEvE,IAAIA,UAAU,IAAIxB,UAAU,EAAE;AAC5B,UAAA,IAAMgD,UAAU,GAAGxB,UAAU,CAACzM,MAAM,CAAA;AAEpC,UAAA,KAAK,IAAIkO,CAAC,GAAGD,UAAU,GAAG,CAAC,EAAEC,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;YACxC,IAAMC,UAAU,GAAGnJ,SAAS,CAACyH,UAAU,CAACyB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;YACjDC,UAAU,CAACC,cAAc,GAAG,CAACV,WAAW,CAACU,cAAc,IAAI,CAAC,IAAI,CAAC,CAAA;YACjEnD,UAAU,CAACsB,YAAY,CAAC4B,UAAU,EAAElJ,cAAc,CAACyI,WAAW,CAAC,CAAC,CAAA;AAClE,WAAA;AACF,SAAA;AACF,OAAA;MAEA3C,YAAY,CAAC2C,WAAW,CAAC,CAAA;AACzB,MAAA,OAAO,IAAI,CAAA;AACb,KAAA;;AAEA;IACA,IAAIA,WAAW,YAAYlJ,OAAO,IAAI,CAACiG,oBAAoB,CAACiD,WAAW,CAAC,EAAE;MACxE3C,YAAY,CAAC2C,WAAW,CAAC,CAAA;AACzB,MAAA,OAAO,IAAI,CAAA;AACb,KAAA;;AAEA;IACA,IACE,CAAC/C,OAAO,KAAK,UAAU,IACrBA,OAAO,KAAK,SAAS,IACrBA,OAAO,KAAK,UAAU,KACxBrL,UAAU,CAAC,6BAA6B,EAAEoO,WAAW,CAACrB,SAAS,CAAC,EAChE;MACAtB,YAAY,CAAC2C,WAAW,CAAC,CAAA;AACzB,MAAA,OAAO,IAAI,CAAA;AACb,KAAA;;AAEA;AACA,IAAA,IAAIlG,kBAAkB,IAAIkG,WAAW,CAACxJ,QAAQ,KAAK,CAAC,EAAE;AACpD;MACAoB,OAAO,GAAGoI,WAAW,CAACN,WAAW,CAAA;MACjC9H,OAAO,GAAGtG,aAAa,CAACsG,OAAO,EAAEtD,eAAa,EAAE,GAAG,CAAC,CAAA;MACpDsD,OAAO,GAAGtG,aAAa,CAACsG,OAAO,EAAErD,UAAQ,EAAE,GAAG,CAAC,CAAA;MAC/CqD,OAAO,GAAGtG,aAAa,CAACsG,OAAO,EAAEpD,aAAW,EAAE,GAAG,CAAC,CAAA;AAClD,MAAA,IAAIwL,WAAW,CAACN,WAAW,KAAK9H,OAAO,EAAE;AACvC/G,QAAAA,SAAS,CAACsF,SAAS,CAACI,OAAO,EAAE;AAAEvD,UAAAA,OAAO,EAAEgN,WAAW,CAAC1I,SAAS,EAAC;AAAE,SAAC,CAAC,CAAA;QAClE0I,WAAW,CAACN,WAAW,GAAG9H,OAAO,CAAA;AACnC,OAAA;AACF,KAAA;;AAEA;AACAkI,IAAAA,YAAY,CAAC,uBAAuB,EAAEE,WAAW,EAAE,IAAI,CAAC,CAAA;AAExD,IAAA,OAAO,KAAK,CAAA;GACb,CAAA;;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACE;EACA,IAAMW,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAaC,KAAK,EAAEC,MAAM,EAAEnN,KAAK,EAAE;AACxD;AACA,IAAA,IACE4G,YAAY,KACXuG,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,MAAM,CAAC,KACrCnN,KAAK,IAAI0B,QAAQ,IAAI1B,KAAK,IAAImI,WAAW,CAAC,EAC3C;AACA,MAAA,OAAO,KAAK,CAAA;AACd,KAAA;;AAEA;AACJ;AACA;AACA;AACI,IAAA,IACElC,eAAe,IACf,CAACF,WAAW,CAACoH,MAAM,CAAC,IACpBjP,UAAU,CAAC6C,WAAS,EAAEoM,MAAM,CAAC,EAC7B,CAED,MAAM,IAAInH,eAAe,IAAI9H,UAAU,CAAC8C,WAAS,EAAEmM,MAAM,CAAC,EAAE,CAG5D,MAAM,IAAI,CAAC/H,YAAY,CAAC+H,MAAM,CAAC,IAAIpH,WAAW,CAACoH,MAAM,CAAC,EAAE;AACvD,MAAA;AACE;AACA;AACA;AACCP,MAAAA,uBAAuB,CAACM,KAAK,CAAC,KAC3B3H,uBAAuB,CAACC,YAAY,YAAYrH,MAAM,IACtDD,UAAU,CAACqH,uBAAuB,CAACC,YAAY,EAAE0H,KAAK,CAAC,IACtD3H,uBAAuB,CAACC,YAAY,YAAY8C,QAAQ,IACvD/C,uBAAuB,CAACC,YAAY,CAAC0H,KAAK,CAAE,CAAC,KAC/C3H,uBAAuB,CAACK,kBAAkB,YAAYzH,MAAM,IAC5DD,UAAU,CAACqH,uBAAuB,CAACK,kBAAkB,EAAEuH,MAAM,CAAC,IAC7D5H,uBAAuB,CAACK,kBAAkB,YAAY0C,QAAQ,IAC7D/C,uBAAuB,CAACK,kBAAkB,CAACuH,MAAM,CAAE,CAAC;AAC1D;AACA;AACCA,MAAAA,MAAM,KAAK,IAAI,IACd5H,uBAAuB,CAACM,8BAA8B,KACpDN,uBAAuB,CAACC,YAAY,YAAYrH,MAAM,IACtDD,UAAU,CAACqH,uBAAuB,CAACC,YAAY,EAAExF,KAAK,CAAC,IACtDuF,uBAAuB,CAACC,YAAY,YAAY8C,QAAQ,IACvD/C,uBAAuB,CAACC,YAAY,CAACxF,KAAK,CAAE,CAAE,EACpD,CAGD,MAAM;AACL,QAAA,OAAO,KAAK,CAAA;AACd,OAAA;AACA;AACF,KAAC,MAAM,IAAIsH,mBAAmB,CAAC6F,MAAM,CAAC,EAAE,CAIvC,MAAM,IACLjP,UAAU,CAAC+C,gBAAc,EAAErD,aAAa,CAACoC,KAAK,EAAEmB,iBAAe,EAAE,EAAE,CAAC,CAAC,EACrE,CAID,MAAM,IACL,CAACgM,MAAM,KAAK,KAAK,IAAIA,MAAM,KAAK,YAAY,IAAIA,MAAM,KAAK,MAAM,KACjED,KAAK,KAAK,QAAQ,IAClBpP,aAAa,CAACkC,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,IACnCoH,aAAa,CAAC8F,KAAK,CAAC,EACpB,CAKD,MAAM,IACLhH,uBAAuB,IACvB,CAAChI,UAAU,CAACgD,mBAAiB,EAAEtD,aAAa,CAACoC,KAAK,EAAEmB,iBAAe,EAAE,EAAE,CAAC,CAAC,EACzE,CAGD,MAAM,IAAInB,KAAK,EAAE;AAChB,MAAA,OAAO,KAAK,CAAA;AACd,KAAC,MAAM,CAEL;AAGF,IAAA,OAAO,IAAI,CAAA;GACZ,CAAA;;AAED;AACF;AACA;AACA;AACA;AACA;AACE,EAAA,IAAM4M,uBAAuB,GAAG,SAA1BA,uBAAuBA,CAAarD,OAAO,EAAE;IACjD,OAAOA,OAAO,KAAK,gBAAgB,IAAI7L,WAAW,CAAC6L,OAAO,EAAElI,gBAAc,CAAC,CAAA;GAC5E,CAAA;;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE,EAAA,IAAM+L,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAad,WAAW,EAAE;AACjD,IAAA,IAAIe,IAAI,CAAA;AACR,IAAA,IAAIrN,KAAK,CAAA;AACT,IAAA,IAAImN,MAAM,CAAA;AACV,IAAA,IAAI9N,CAAC,CAAA;AACL;AACA+M,IAAAA,YAAY,CAAC,0BAA0B,EAAEE,WAAW,EAAE,IAAI,CAAC,CAAA;AAE3D,IAAA,IAAQL,UAAU,GAAKK,WAAW,CAA1BL,UAAU,CAAA;;AAElB;AACA,IAAA,IAAI,CAACA,UAAU,IAAIJ,YAAY,CAACS,WAAW,CAAC,EAAE;AAC5C,MAAA,OAAA;AACF,KAAA;AAEA,IAAA,IAAMgB,SAAS,GAAG;AAChBC,MAAAA,QAAQ,EAAE,EAAE;AACZC,MAAAA,SAAS,EAAE,EAAE;AACbC,MAAAA,QAAQ,EAAE,IAAI;AACdC,MAAAA,iBAAiB,EAAEtI,YAAAA;KACpB,CAAA;IACD/F,CAAC,GAAG4M,UAAU,CAACrN,MAAM,CAAA;;AAErB;IACA,OAAOS,CAAC,EAAE,EAAE;AACVgO,MAAAA,IAAI,GAAGpB,UAAU,CAAC5M,CAAC,CAAC,CAAA;MACpB,IAAAsO,KAAA,GAA+BN,IAAI;QAA3BnD,IAAI,GAAAyD,KAAA,CAAJzD,IAAI;QAAEV,YAAY,GAAAmE,KAAA,CAAZnE,YAAY,CAAA;AAC1BxJ,MAAAA,KAAK,GAAGkK,IAAI,KAAK,OAAO,GAAGmD,IAAI,CAACrN,KAAK,GAAGhC,UAAU,CAACqP,IAAI,CAACrN,KAAK,CAAC,CAAA;AAC9DmN,MAAAA,MAAM,GAAGhO,iBAAiB,CAAC+K,IAAI,CAAC,CAAA;;AAEhC;MACAoD,SAAS,CAACC,QAAQ,GAAGJ,MAAM,CAAA;MAC3BG,SAAS,CAACE,SAAS,GAAGxN,KAAK,CAAA;MAC3BsN,SAAS,CAACG,QAAQ,GAAG,IAAI,CAAA;AACzBH,MAAAA,SAAS,CAACM,aAAa,GAAGpL,SAAS,CAAC;AACpC4J,MAAAA,YAAY,CAAC,uBAAuB,EAAEE,WAAW,EAAEgB,SAAS,CAAC,CAAA;MAC7DtN,KAAK,GAAGsN,SAAS,CAACE,SAAS,CAAA;;AAE3B;MACA,IAAIF,SAAS,CAACM,aAAa,EAAE;AAC3B,QAAA,SAAA;AACF,OAAA;;AAEA;AACA3D,MAAAA,gBAAgB,CAACC,IAAI,EAAEoC,WAAW,CAAC,CAAA;;AAEnC;AACA,MAAA,IAAI,CAACgB,SAAS,CAACG,QAAQ,EAAE;AACvB,QAAA,SAAA;AACF,OAAA;;AAEA;MACA,IAAI,CAACtH,wBAAwB,IAAIjI,UAAU,CAAC,MAAM,EAAE8B,KAAK,CAAC,EAAE;AAC1DiK,QAAAA,gBAAgB,CAACC,IAAI,EAAEoC,WAAW,CAAC,CAAA;AACnC,QAAA,SAAA;AACF,OAAA;;AAEA;AACA,MAAA,IAAIlG,kBAAkB,EAAE;QACtBpG,KAAK,GAAGpC,aAAa,CAACoC,KAAK,EAAEY,eAAa,EAAE,GAAG,CAAC,CAAA;QAChDZ,KAAK,GAAGpC,aAAa,CAACoC,KAAK,EAAEa,UAAQ,EAAE,GAAG,CAAC,CAAA;QAC3Cb,KAAK,GAAGpC,aAAa,CAACoC,KAAK,EAAEc,aAAW,EAAE,GAAG,CAAC,CAAA;AAChD,OAAA;;AAEA;AACA,MAAA,IAAMoM,KAAK,GAAG/N,iBAAiB,CAACmN,WAAW,CAACP,QAAQ,CAAC,CAAA;MACrD,IAAI,CAACkB,iBAAiB,CAACC,KAAK,EAAEC,MAAM,EAAEnN,KAAK,CAAC,EAAE;AAC5C,QAAA,SAAA;AACF,OAAA;;AAEA;AACN;AACA;MACM,IAAI6G,oBAAoB,KAAKsG,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,MAAM,CAAC,EAAE;AAClE;AACAlD,QAAAA,gBAAgB,CAACC,IAAI,EAAEoC,WAAW,CAAC,CAAA;;AAEnC;QACAtM,KAAK,GAAG8G,2BAA2B,GAAG9G,KAAK,CAAA;AAC7C,OAAA;;AAEA;MACA,IAAIqG,YAAY,IAAInI,UAAU,CAAC,+BAA+B,EAAE8B,KAAK,CAAC,EAAE;AACtEiK,QAAAA,gBAAgB,CAACC,IAAI,EAAEoC,WAAW,CAAC,CAAA;AACnC,QAAA,SAAA;AACF,OAAA;;AAEA;AACA,MAAA,IACElI,kBAAkB,IAClBzC,OAAA,CAAOF,YAAY,CAAK,KAAA,QAAQ,IAChC,OAAOA,YAAY,CAACoM,gBAAgB,KAAK,UAAU,EACnD;AACA,QAAA,IAAIrE,YAAY,EAAE,CAEjB,MAAM;AACL,UAAA,QAAQ/H,YAAY,CAACoM,gBAAgB,CAACX,KAAK,EAAEC,MAAM,CAAC;AAClD,YAAA,KAAK,aAAa;AAAE,cAAA;AAClBnN,gBAAAA,KAAK,GAAGoE,kBAAkB,CAACjC,UAAU,CAACnC,KAAK,CAAC,CAAA;AAC5C,gBAAA,MAAA;AACF,eAAA;AAEA,YAAA,KAAK,kBAAkB;AAAE,cAAA;AACvBA,gBAAAA,KAAK,GAAGoE,kBAAkB,CAAChC,eAAe,CAACpC,KAAK,CAAC,CAAA;AACjD,gBAAA,MAAA;AACF,eAAA;AAKF,WAAA;AACF,SAAA;AACF,OAAA;;AAEA;MACA,IAAI;AACF,QAAA,IAAIwJ,YAAY,EAAE;UAChB8C,WAAW,CAACwB,cAAc,CAACtE,YAAY,EAAEU,IAAI,EAAElK,KAAK,CAAC,CAAA;AACvD,SAAC,MAAM;AACL;AACAsM,UAAAA,WAAW,CAAC/B,YAAY,CAACL,IAAI,EAAElK,KAAK,CAAC,CAAA;AACvC,SAAA;AAEA,QAAA,IAAI6L,YAAY,CAACS,WAAW,CAAC,EAAE;UAC7B3C,YAAY,CAAC2C,WAAW,CAAC,CAAA;AAC3B,SAAC,MAAM;AACLrP,UAAAA,QAAQ,CAACwF,SAAS,CAACI,OAAO,CAAC,CAAA;AAC7B,SAAA;AACF,OAAC,CAAC,OAAOP,CAAC,EAAE,EAAC;AACf,KAAA;;AAEA;AACA8J,IAAAA,YAAY,CAAC,yBAAyB,EAAEE,WAAW,EAAE,IAAI,CAAC,CAAA;GAC3D,CAAA;;AAED;AACF;AACA;AACA;AACA;AACE,EAAA,IAAMyB,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAaC,QAAQ,EAAE;AAC7C,IAAA,IAAIC,UAAU,CAAA;AACd,IAAA,IAAMC,cAAc,GAAG3C,eAAe,CAACyC,QAAQ,CAAC,CAAA;;AAEhD;AACA5B,IAAAA,YAAY,CAAC,yBAAyB,EAAE4B,QAAQ,EAAE,IAAI,CAAC,CAAA;AAEvD,IAAA,OAAQC,UAAU,GAAGC,cAAc,CAACC,QAAQ,EAAE,EAAG;AAC/C;AACA/B,MAAAA,YAAY,CAAC,wBAAwB,EAAE6B,UAAU,EAAE,IAAI,CAAC,CAAA;AACxD;MACAxB,iBAAiB,CAACwB,UAAU,CAAC,CAAA;;AAE7B;MACAb,mBAAmB,CAACa,UAAU,CAAC,CAAA;;AAE/B;AACA,MAAA,IAAIA,UAAU,CAAC/J,OAAO,YAAYjB,gBAAgB,EAAE;AAClD8K,QAAAA,kBAAkB,CAACE,UAAU,CAAC/J,OAAO,CAAC,CAAA;AACxC,OAAA;AACF,KAAA;;AAEA;AACAkI,IAAAA,YAAY,CAAC,wBAAwB,EAAE4B,QAAQ,EAAE,IAAI,CAAC,CAAA;GACvD,CAAA;;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACE;AACAvL,EAAAA,SAAS,CAAC2L,QAAQ,GAAG,UAAU3D,KAAK,EAAY;AAAA,IAAA,IAAVjC,GAAG,GAAA7J,SAAA,CAAAC,MAAA,GAAA,CAAA,IAAAD,SAAA,CAAA,CAAA,CAAA,KAAA6D,SAAA,GAAA7D,SAAA,CAAA,CAAA,CAAA,GAAG,EAAE,CAAA;AAC5C,IAAA,IAAIuM,IAAI,CAAA;AACR,IAAA,IAAImD,YAAY,CAAA;AAChB,IAAA,IAAI/B,WAAW,CAAA;AACf,IAAA,IAAIgC,OAAO,CAAA;AACX,IAAA,IAAIC,UAAU,CAAA;AACd;AACJ;AACA;IACI3G,cAAc,GAAG,CAAC6C,KAAK,CAAA;AACvB,IAAA,IAAI7C,cAAc,EAAE;AAClB6C,MAAAA,KAAK,GAAG,OAAO,CAAA;AACjB,KAAA;;AAEA;IACA,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,CAAC0B,OAAO,CAAC1B,KAAK,CAAC,EAAE;AAChD,MAAA,IAAI,OAAOA,KAAK,CAAChN,QAAQ,KAAK,UAAU,EAAE;AACxCgN,QAAAA,KAAK,GAAGA,KAAK,CAAChN,QAAQ,EAAE,CAAA;AACxB,QAAA,IAAI,OAAOgN,KAAK,KAAK,QAAQ,EAAE;UAC7B,MAAMpM,eAAe,CAAC,iCAAiC,CAAC,CAAA;AAC1D,SAAA;AACF,OAAC,MAAM;QACL,MAAMA,eAAe,CAAC,4BAA4B,CAAC,CAAA;AACrD,OAAA;AACF,KAAA;;AAEA;AACA,IAAA,IAAI,CAACoE,SAAS,CAACM,WAAW,EAAE;AAC1B,MAAA,IACEpB,OAAA,CAAOJ,MAAM,CAACiN,YAAY,CAAK,KAAA,QAAQ,IACvC,OAAOjN,MAAM,CAACiN,YAAY,KAAK,UAAU,EACzC;AACA,QAAA,IAAI,OAAO/D,KAAK,KAAK,QAAQ,EAAE;AAC7B,UAAA,OAAOlJ,MAAM,CAACiN,YAAY,CAAC/D,KAAK,CAAC,CAAA;AACnC,SAAA;AAEA,QAAA,IAAI0B,OAAO,CAAC1B,KAAK,CAAC,EAAE;AAClB,UAAA,OAAOlJ,MAAM,CAACiN,YAAY,CAAC/D,KAAK,CAACV,SAAS,CAAC,CAAA;AAC7C,SAAA;AACF,OAAA;AAEA,MAAA,OAAOU,KAAK,CAAA;AACd,KAAA;;AAEA;IACA,IAAI,CAAClE,UAAU,EAAE;MACfgC,YAAY,CAACC,GAAG,CAAC,CAAA;AACnB,KAAA;;AAEA;IACA/F,SAAS,CAACI,OAAO,GAAG,EAAE,CAAA;;AAEtB;AACA,IAAA,IAAI,OAAO4H,KAAK,KAAK,QAAQ,EAAE;AAC7BzD,MAAAA,QAAQ,GAAG,KAAK,CAAA;AAClB,KAAA;AAEA,IAAA,IAAIA,QAAQ,EAAE;AACZ;MACA,IAAIyD,KAAK,CAACsB,QAAQ,EAAE;AAClB,QAAA,IAAMxC,OAAO,GAAGpK,iBAAiB,CAACsL,KAAK,CAACsB,QAAQ,CAAC,CAAA;QACjD,IAAI,CAAC/G,YAAY,CAACuE,OAAO,CAAC,IAAIzD,WAAW,CAACyD,OAAO,CAAC,EAAE;UAClD,MAAMlL,eAAe,CACnB,yDACF,CAAC,CAAA;AACH,SAAA;AACF,OAAA;AACF,KAAC,MAAM,IAAIoM,KAAK,YAAYtH,IAAI,EAAE;AAChC;AACN;AACM+H,MAAAA,IAAI,GAAGV,aAAa,CAAC,SAAS,CAAC,CAAA;MAC/B6D,YAAY,GAAGnD,IAAI,CAAC/G,aAAa,CAACQ,UAAU,CAAC8F,KAAK,EAAE,IAAI,CAAC,CAAA;MACzD,IAAI4D,YAAY,CAACvL,QAAQ,KAAK,CAAC,IAAIuL,YAAY,CAACtC,QAAQ,KAAK,MAAM,EAAE;AACnE;AACAb,QAAAA,IAAI,GAAGmD,YAAY,CAAA;AACrB,OAAC,MAAM,IAAIA,YAAY,CAACtC,QAAQ,KAAK,MAAM,EAAE;AAC3Cb,QAAAA,IAAI,GAAGmD,YAAY,CAAA;AACrB,OAAC,MAAM;AACL;AACAnD,QAAAA,IAAI,CAACuD,WAAW,CAACJ,YAAY,CAAC,CAAA;AAChC,OAAA;AACF,KAAC,MAAM;AACL;AACA,MAAA,IACE,CAAC5H,UAAU,IACX,CAACL,kBAAkB,IACnB,CAACE,cAAc;AACf;MACAmE,KAAK,CAAC1M,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EACzB;QACA,OAAOqG,kBAAkB,IAAIuC,mBAAmB,GAC5CvC,kBAAkB,CAACjC,UAAU,CAACsI,KAAK,CAAC,GACpCA,KAAK,CAAA;AACX,OAAA;;AAEA;AACAS,MAAAA,IAAI,GAAGV,aAAa,CAACC,KAAK,CAAC,CAAA;;AAE3B;MACA,IAAI,CAACS,IAAI,EAAE;QACT,OAAOzE,UAAU,GAAG,IAAI,GAAGE,mBAAmB,GAAGtC,SAAS,GAAG,EAAE,CAAA;AACjE,OAAA;AACF,KAAA;;AAEA;IACA,IAAI6G,IAAI,IAAI1E,UAAU,EAAE;AACtBmD,MAAAA,YAAY,CAACuB,IAAI,CAACwD,UAAU,CAAC,CAAA;AAC/B,KAAA;;AAEA;IACA,IAAMC,YAAY,GAAGpD,eAAe,CAACvE,QAAQ,GAAGyD,KAAK,GAAGS,IAAI,CAAC,CAAA;;AAE7D;AACA,IAAA,OAAQoB,WAAW,GAAGqC,YAAY,CAACR,QAAQ,EAAE,EAAG;AAC9C;MACA,IAAI7B,WAAW,CAACxJ,QAAQ,KAAK,CAAC,IAAIwJ,WAAW,KAAKgC,OAAO,EAAE;AACzD,QAAA,SAAA;AACF,OAAA;;AAEA;MACA7B,iBAAiB,CAACH,WAAW,CAAC,CAAA;;AAE9B;MACAc,mBAAmB,CAACd,WAAW,CAAC,CAAA;;AAEhC;AACA,MAAA,IAAIA,WAAW,CAACpI,OAAO,YAAYjB,gBAAgB,EAAE;AACnD8K,QAAAA,kBAAkB,CAACzB,WAAW,CAACpI,OAAO,CAAC,CAAA;AACzC,OAAA;AAEAoK,MAAAA,OAAO,GAAGhC,WAAW,CAAA;AACvB,KAAA;AAEAgC,IAAAA,OAAO,GAAG,IAAI,CAAA;;AAEd;AACA,IAAA,IAAItH,QAAQ,EAAE;AACZ,MAAA,OAAOyD,KAAK,CAAA;AACd,KAAA;;AAEA;AACA,IAAA,IAAIhE,UAAU,EAAE;AACd,MAAA,IAAIC,mBAAmB,EAAE;QACvB6H,UAAU,GAAG9J,sBAAsB,CAAC6G,IAAI,CAACJ,IAAI,CAAC/G,aAAa,CAAC,CAAA;QAE5D,OAAO+G,IAAI,CAACwD,UAAU,EAAE;AACtB;AACAH,UAAAA,UAAU,CAACE,WAAW,CAACvD,IAAI,CAACwD,UAAU,CAAC,CAAA;AACzC,SAAA;AACF,OAAC,MAAM;AACLH,QAAAA,UAAU,GAAGrD,IAAI,CAAA;AACnB,OAAA;AAEA,MAAA,IAAI9F,YAAY,CAACwJ,UAAU,IAAIxJ,YAAY,CAACyJ,aAAa,EAAE;AACzD;AACR;AACA;AACA;AACA;AACA;AACA;QACQN,UAAU,GAAG5J,UAAU,CAAC2G,IAAI,CAACtI,gBAAgB,EAAEuL,UAAU,EAAE,IAAI,CAAC,CAAA;AAClE,OAAA;AAEA,MAAA,OAAOA,UAAU,CAAA;AACnB,KAAA;IAEA,IAAIO,cAAc,GAAGxI,cAAc,GAAG4E,IAAI,CAACnB,SAAS,GAAGmB,IAAI,CAACD,SAAS,CAAA;;AAErE;AACA,IAAA,IACE3E,cAAc,IACdtB,YAAY,CAAC,UAAU,CAAC,IACxBkG,IAAI,CAAC/G,aAAa,IAClB+G,IAAI,CAAC/G,aAAa,CAAC4K,OAAO,IAC1B7D,IAAI,CAAC/G,aAAa,CAAC4K,OAAO,CAAC7E,IAAI,IAC/BhM,UAAU,CAAC6G,YAAwB,EAAEmG,IAAI,CAAC/G,aAAa,CAAC4K,OAAO,CAAC7E,IAAI,CAAC,EACrE;AACA4E,MAAAA,cAAc,GACZ,YAAY,GAAG5D,IAAI,CAAC/G,aAAa,CAAC4K,OAAO,CAAC7E,IAAI,GAAG,KAAK,GAAG4E,cAAc,CAAA;AAC3E,KAAA;;AAEA;AACA,IAAA,IAAI1I,kBAAkB,EAAE;MACtB0I,cAAc,GAAGlR,aAAa,CAACkR,cAAc,EAAElO,eAAa,EAAE,GAAG,CAAC,CAAA;MAClEkO,cAAc,GAAGlR,aAAa,CAACkR,cAAc,EAAEjO,UAAQ,EAAE,GAAG,CAAC,CAAA;MAC7DiO,cAAc,GAAGlR,aAAa,CAACkR,cAAc,EAAEhO,aAAW,EAAE,GAAG,CAAC,CAAA;AAClE,KAAA;IAEA,OAAOsD,kBAAkB,IAAIuC,mBAAmB,GAC5CvC,kBAAkB,CAACjC,UAAU,CAAC2M,cAAc,CAAC,GAC7CA,cAAc,CAAA;GACnB,CAAA;;AAED;AACF;AACA;AACA;AACA;AACA;AACErM,EAAAA,SAAS,CAACuM,SAAS,GAAG,UAAUxG,GAAG,EAAE;IACnCD,YAAY,CAACC,GAAG,CAAC,CAAA;AACjBjC,IAAAA,UAAU,GAAG,IAAI,CAAA;GAClB,CAAA;;AAED;AACF;AACA;AACA;AACA;EACE9D,SAAS,CAACwM,WAAW,GAAG,YAAY;AAClC/G,IAAAA,MAAM,GAAG,IAAI,CAAA;AACb3B,IAAAA,UAAU,GAAG,KAAK,CAAA;GACnB,CAAA;;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE9D,SAAS,CAACyM,gBAAgB,GAAG,UAAUC,GAAG,EAAE9B,IAAI,EAAErN,KAAK,EAAE;AACvD;IACA,IAAI,CAACkI,MAAM,EAAE;MACXK,YAAY,CAAC,EAAE,CAAC,CAAA;AAClB,KAAA;AAEA,IAAA,IAAM2E,KAAK,GAAG/N,iBAAiB,CAACgQ,GAAG,CAAC,CAAA;AACpC,IAAA,IAAMhC,MAAM,GAAGhO,iBAAiB,CAACkO,IAAI,CAAC,CAAA;AACtC,IAAA,OAAOJ,iBAAiB,CAACC,KAAK,EAAEC,MAAM,EAAEnN,KAAK,CAAC,CAAA;GAC/C,CAAA;;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACEyC,EAAAA,SAAS,CAAC2M,OAAO,GAAG,UAAU/C,UAAU,EAAEgD,YAAY,EAAE;AACtD,IAAA,IAAI,OAAOA,YAAY,KAAK,UAAU,EAAE;AACtC,MAAA,OAAA;AACF,KAAA;IAEAxK,KAAK,CAACwH,UAAU,CAAC,GAAGxH,KAAK,CAACwH,UAAU,CAAC,IAAI,EAAE,CAAA;AAC3ClP,IAAAA,SAAS,CAAC0H,KAAK,CAACwH,UAAU,CAAC,EAAEgD,YAAY,CAAC,CAAA;GAC3C,CAAA;;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACE5M,EAAAA,SAAS,CAAC6M,UAAU,GAAG,UAAUjD,UAAU,EAAE;AAC3C,IAAA,IAAIxH,KAAK,CAACwH,UAAU,CAAC,EAAE;AACrB,MAAA,OAAOpP,QAAQ,CAAC4H,KAAK,CAACwH,UAAU,CAAC,CAAC,CAAA;AACpC,KAAA;GACD,CAAA;;AAED;AACF;AACA;AACA;AACA;AACA;AACE5J,EAAAA,SAAS,CAAC8M,WAAW,GAAG,UAAUlD,UAAU,EAAE;AAC5C,IAAA,IAAIxH,KAAK,CAACwH,UAAU,CAAC,EAAE;AACrBxH,MAAAA,KAAK,CAACwH,UAAU,CAAC,GAAG,EAAE,CAAA;AACxB,KAAA;GACD,CAAA;;AAED;AACF;AACA;AACA;AACA;EACE5J,SAAS,CAAC+M,cAAc,GAAG,YAAY;IACrC3K,KAAK,GAAG,EAAE,CAAA;GACX,CAAA;AAED,EAAA,OAAOpC,SAAS,CAAA;AAClB,CAAA;AAEA,aAAeF,eAAe,EAAE;;;;"}